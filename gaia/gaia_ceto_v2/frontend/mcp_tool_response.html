<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tool Response – Mock Tools Only</title>

  <!-- Styles -->
  <link rel="stylesheet" type="text/css" href="../../djangaia/ceto_chat/static/ceto_chat/ceto_chat.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <!-- Vue ESM via import map -->
  <script type="importmap">
  { "imports": { "vue": "https://unpkg.com/vue@3.4.38/dist/vue.esm-browser.js" } }
  </script>
</head>
<body>
  <div id="app" class="app-container">
    <!-- Debug toggle -->
    <button @click="toggleDebugPanel"
            class="btn btn-sm debug-toggle"
            :class="debugPanelVisible ? 'btn-warning' : 'btn-outline-secondary'"
            style="position: fixed; top: 10px; right: 10px; z-index: 1050; background: #333; color: white; border: 1px solid #666;">
      <i class="fas fa-bug"></i> Debug
    </button>

    <div class="main-content">
      <h1 style="margin-top:0;">Tool Response – Mock Tools</h1>

      <!-- Simple controls: pick a mock tool and optional text -->
      <div style="display:flex; gap:10px; align-items:center; flex-wrap:wrap; margin:10px 0;">
        <select v-model="selectedToolName" style="padding:8px; min-width:240px; border:1px solid #ccc; border-radius:3px;">
          <option disabled value="">Select mock tool</option>
          <option v-for="t in mockTools" :key="t.name" :value="t.name">{{ t.name }}</option>
        </select>
        <input v-model="userText" type="text" placeholder="Optional text arg" style="padding:8px; width: 320px; border:1px solid #ccc; border-radius:3px;">
        <button @click="callMockTool" :disabled="mcpToolLoading || !selectedToolName"
                style="padding:8px 16px; background:#007bff; color:#fff; border:none; border-radius:3px; cursor:pointer;"
                :style="{ opacity: (mcpToolLoading || !selectedToolName) ? 0.6 : 1 }">
          <span v-if="mcpToolLoading"><i class="fas fa-spinner fa-spin"></i> Calling…</span>
          <span v-else>Call</span>
        </button>
      </div>

      <!-- Tool response rendering (unified panel) -->
      <chat-response-panel :response="lastToolResponse" v-model="showFullJsonRpc"></chat-response-panel>
    </div>

    <!-- Debug Panel -->
    <debug-panel :show-tools-tab="true"></debug-panel>
  </div>

  <!-- Minimal app (ESM) -->
  <script type="module">
    import { createApp } from 'vue';
    import { DebugPanelMixin, DebugPanelComponent } from '../../djangaia/ceto_chat/static/ceto_chat/debug.js';
    import ChatResponsePanel from '../../djangaia/ceto_chat/static/ceto_chat/chat_response.module.js';
    import { CetoMockTools } from '../../djangaia/ceto_chat/static/ceto_chat/mock_tools.js';

    const app = createApp({
      mixins: [DebugPanelMixin],
      data() {
        return {
          mockTools: [], selectedToolName: '', userText: '',
          mcpToolLoading: false, lastToolResponse: null, showFullJsonRpc: false,
          mcpTools: [], mcpConnected: false, mcpLoading: false, mockModeActive: true, mcpServerUrl: ''
        };
      },
      methods: {
        async callMockTool() {
          if (!this.selectedToolName) return;
          const tool = CetoMockTools && CetoMockTools[this.selectedToolName];
          if (!tool || typeof tool.invoke !== 'function') {
            this.lastToolResponse = { success: false, error: 'Mock tool not found' };
            return;
          }
          this.mcpToolLoading = true; this.lastToolResponse = null;
          try {
            const res = await tool.invoke(this, { text: this.userText });
            this.lastToolResponse = res;
          } finally { this.mcpToolLoading = false; }
        },
        getQueryParam(name) { const urlParams = new URLSearchParams(window.location.search); return urlParams.get(name); }
      },
      mounted() {
        this.mockTools = Object.entries(CetoMockTools || {}).map(([name, def]) => ({ name, description: def.description }));
        const toolParam = this.getQueryParam('tool');
        const textParam = this.getQueryParam('text');
        const autoParam = this.getQueryParam('auto');
        if (toolParam && this.mockTools.find(t => t.name === toolParam)) {
          this.selectedToolName = toolParam; if (textParam) this.userText = textParam;
          if (autoParam === 'true') { this.$nextTick(() => { this.callMockTool(); }); }
        }
      }
    });

    app.component('chat-response-panel', ChatResponsePanel);
    app.component('debug-panel', DebugPanelComponent);

    app.mount('#app');
  </script>
</body>
</html>

