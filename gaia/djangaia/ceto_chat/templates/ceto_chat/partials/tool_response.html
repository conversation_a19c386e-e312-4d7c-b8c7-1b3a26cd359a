<!-- MCP Tool Response Partial (shared UI for tool testing/response display) -->
<div class="test-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
    <h3>MCP Tool Test</h3>
    <p>Test calling an MCP tool and inspect logs in the debug panel.</p>

    <div style="margin: 10px 0; display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
        <!-- Tool dropdown -->
        <select v-model="selectedToolName"
                :disabled="mcpLoading || !mcpTools.length"
                style="padding: 8px; min-width: 220px; border: 1px solid #ccc; border-radius: 3px;">
            <option disabled value="">Select a tool</option>
            <option v-for="t in mcpTools" :key="t.name" :value="t.name" v-text="t.name"></option>
        </select>

        <!-- Text input for testMessage -->
        <input v-model="testMessage"
               type="text"
               placeholder="Enter message"
               style="padding: 8px; width: 300px; border: 1px solid #ccc; border-radius: 3px;">

        <!-- Action button calls the selected tool -->
        <button @click="callTool(selectedToolName, { text: testMessage })"
                :disabled="mcpToolLoading || !selectedToolName"
                style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;"
                :style="{ opacity: (mcpToolLoading || !selectedToolName) ? 0.6 : 1 }">
            <span v-if="mcpToolLoading">Calling...</span>
            <span v-else v-text="selectedToolName ? ('Call ' + selectedToolName) : 'Call Tool'"></span>
        </button>
    </div>
    <div v-if="selectedToolName" style="margin-top: 8px; color: #666;">
        <small>
            <strong>Selected:</strong> <span v-text="selectedToolName"></span>
            <span v-if="mcpTools.find(t => t.name === selectedToolName)?.description"
                  v-text="' — ' + mcpTools.find(t => t.name === selectedToolName).description"></span>
        </small>
    </div>

    <!-- Unified response display -->
    <chat-response-panel :response="lastToolResponse" v-model="showFullJsonRpc"></chat-response-panel>
</div>

