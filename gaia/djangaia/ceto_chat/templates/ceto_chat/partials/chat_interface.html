{% verbatim %}

<!-- Chat interface partial extracted from chat_response.html (inner content only) -->
<h1 style="display:flex; align-items:center; gap:8px;">
    <PERSON>to Chat – Minimal MCP Chat
    <span v-if="mockModeActive"
          style="padding: 3px 8px; font-size: 12px; line-height: 1; border-radius: 10px; background:#343a40; color:#ffc107; border:1px solid #6c757d;">
        Mock mode
    </span>
</h1>

<!-- Connection Info (no provider label; show MCP server URL when known) -->
<div style="margin: 10px 0; color:#666; font-size: 13px;">
    <span v-if="mcpConnected"><strong>MCP Server:</strong> {{ mcpServerDisplay }}</span>
    <span v-else><strong>MCP Server:</strong> (not connected)</span>
</div>

<!-- MCP Connection Status -->
<div class="mcp-status-bar" style="margin: 10px 0; padding: 8px; border-radius: 4px; font-size: 14px;"
     :style="{ backgroundColor: mcpConnected ? '#d4edda' : '#f8d7da',
               color: mcpConnected ? '#155724' : '#721c24',
               border: mcpConnected ? '1px solid #c3e6cb' : '1px solid #f5c6cb' }">
    <i :class="mcpConnected ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'"></i>
    <span v-if="mcpLoading">Checking MCP server...</span>
    <span v-else-if="mcpConnected">Connected to MCP server at {{ mcpServerDisplay }}</span>
    <span v-else>MCP server unavailable - using mock chat fallback</span>
    <button @click="loadMcpTools"
            class="btn btn-sm btn-outline-secondary"
            style="float: right; margin-top: -2px; font-size: 12px; padding: 2px 6px;"
            :disabled="mcpLoading">
        <i class="fas fa-sync-alt" :class="{ 'fa-spin': mcpLoading }"></i> Refresh
    </button>
</div>

<!-- Chat layout: scrollable transcript + sticky input bar -->
<div class="chat-pane" style="display:flex; flex-direction:column; height: calc(100vh - 220px); gap: 10px;">
  <!-- Transcript area -->
  <div id="chat-transcript" style="flex:1; overflow-y:auto; border:1px solid #eee; border-radius:4px; padding:10px;">
    <!-- Selected tool info -->
    <div v-if="selectedToolName" style="margin-bottom: 6px; color: #666;">
      <small v-if="selectedToolName === 'raw_chat'"><strong>Selected:</strong> raw_chat — Direct chat via MCP HTTP (fallback to mock_chat when unavailable)</small>
      <small v-else><strong>Selected:</strong> <span v-text="selectedToolName"></span>
        <span v-if="mcpTools.find(t => t.name === selectedToolName)?.description"
              v-text="' — ' + mcpTools.find(t => t.name === selectedToolName).description"></span>
      </small>
    </div>

    <!-- Unified response panel for both raw chat and tool responses -->
    <div>
      <!-- Single ordered view combining chat and tool responses -->
      <div v-for="item in renderItems" :key="item.id" style="margin: 6px 0;">
        <template v-if="item.kind === 'chat'">
          <div class="message-item"><span class="role">{{ item.role }}:</span> <span v-text="item.text"></span></div>
        </template>
        <template v-else-if="item.kind === 'tool'">
          <small v-if="item.status === 'pending' && !item.result" style="color:#666;">Tool: {{ item.toolName }} — running…</small>
          <chat-response-panel v-else :response="item.result" :show-transcript="false" v-model="showFullJsonRpc"></chat-response-panel>
        </template>
      </div>
    </div>
  </div>

  <!-- Sticky input bar -->
  <div class="chat-input" style="position: sticky; bottom: 0; background: #fff; padding: 8px 0; border-top: 1px solid #eee;">
    <div style="display:flex; gap: 10px; align-items:center; flex-wrap: wrap;">
      <input v-model="userMessage"
             @keydown.enter.prevent="handleSendUnified"
             type="text"
             placeholder="Message… Type / for tools"
             style="padding:8px; flex:1 1 360px; border:1px solid #ccc; border-radius:3px;">
      <button @click="handleSendUnified"
              :disabled="(sending || mcpToolLoading) || !userMessage.trim()"
              style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;"
              :style="{ opacity: ((sending || mcpToolLoading) || !userMessage.trim()) ? 0.6 : 1 }">
        <span v-if="sending || mcpToolLoading"><i class="fas fa-spinner fa-spin"></i> Sending…</span>
        <span v-else>Send</span>
      </button>
    </div>
  </div>
</div>


{% endverbatim %}


