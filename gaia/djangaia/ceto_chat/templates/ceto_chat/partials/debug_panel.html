<!-- Debug Panel Partial -->
<div v-if="debugPanelVisible" class="debug-panel">
    <div class="debug-header">
        <h5>Debug Console</h5>
        <div class="debug-controls">
            <button @click="resetAllState"
                    class="btn btn-sm btn-outline-warning"
                    title="Reset all debug state and re-initialize">
                <i class="fas fa-redo"></i> RESET
            </button>
            <button @click="clearDebugLogs" class="btn btn-sm btn-outline-danger">Clear</button>
            <button @click="toggleDebugPanel" class="btn btn-sm btn-outline-secondary">×</button>
        </div>
    </div>

    <!-- Debug Pane Mode Tabs -->
    <div class="debug-tabs">
        <button @click="setDebugPaneMode('calls')"
                class="debug-tab"
                :class="{'active': debugPaneMode === 'calls'}">
            Call Details
        </button>
        <button @click="setDebugPaneMode('api')"
                class="debug-tab"
                :class="{'active': debugPaneMode === 'api'}">
            API Communications
        </button>
        <button @click="setDebugPaneMode('history')"
                class="debug-tab"
                :class="{'active': debugPaneMode === 'history'}">
            Conversation History
        </button>
        <button @click="setDebugPaneMode('tools')"
                class="debug-tab"
                :class="{'active': debugPaneMode === 'tools'}">
            Tools Listing
        </button>
        <button @click="setDebugPaneMode('errors')"
                class="debug-tab"
                :class="{'active': debugPaneMode === 'errors'}">
            Errors <span v-if="errorLog.length > 0" class="badge badge-danger">{{ errorLog.length }}</span>
        </button>
    </div>

    <!-- Call Details Pane -->
    <div v-if="debugPaneMode === 'calls'" class="debug-content">
        <h6>HTTP Call Details</h6>
        <div v-if="callDetails.length === 0" class="debug-empty">
            No HTTP calls tracked yet. Send a message to see call details.
        </div>
        <div v-else>
            <div class="call-details-table">
                <div class="call-details-header">
                    <span>Time</span>
                    <span>Method</span>
                    <span>URL</span>
                    <span>Status</span>
                    <span>Latency</span>
                    <span>Sent</span>
                    <span>Received</span>
                </div>
                <div v-for="call in callDetails"
                     :key="call.id"
                     class="call-details-row"
                     :class="{'call-error': call.status >= 400 || call.status === 'ERROR', 'call-pending': !call.completed}">
                    <span class="call-time" v-text="formatTimestamp(call.timestamp)"></span>
                    <span class="call-method" v-text="call.method"></span>
                    <span class="call-url">
                        <a href="#" @click.prevent="toggleCallExpand(call)" :title="call.url" v-text="call.url"></a>
                    </span>
                    <span class="call-status" v-text="call.status"></span>
                    <span class="call-latency" v-text="call.latency ? call.latency + ' ms' : (call.completed ? '-' : 'pending')"></span>
                    <span class="call-sent" v-text="formatBytes(call.sentBytes)"></span>
                    <span class="call-received" v-text="formatBytes(call.receivedBytes)"></span>

                    <!-- Expanded details below the row spanning all columns -->
                    <div v-if="call.expanded" class="call-row-details">
                        <div style="margin-bottom:6px"><strong>URL:</strong> {{ call.url }}</div>
                        <div v-if="call.requestBody" class="call-detail-section">
                            <h7>Request</h7>
                            <pre class="debug-log-data" v-text="call.requestBody"></pre>
                        </div>
                        <div v-if="call.responseBody" class="call-detail-section">
                            <h7>Response</h7>
                            <pre class="debug-log-data" v-text="call.responseBody"></pre>
                        </div>
                        <div v-if="call.error" class="call-detail-section">
                            <h7>Error</h7>
                            <pre class="debug-log-data error-text" v-text="call.error"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Communications Pane -->
    <div v-if="debugPaneMode === 'api'" class="debug-content">
        <h6>Raw API Data</h6>
        <div v-if="debugLogs.length === 0" class="debug-empty">
            No debug logs yet. Send a message to see server communications.
        </div>
        <div v-for="log in debugLogs" :key="log.id" class="debug-log-entry" :class="'debug-' + log.type.toLowerCase()">
            <div class="debug-log-header">
                <span class="debug-timestamp" v-text="formatTimestamp(log.timestamp)"></span>
                <span class="debug-type" :class="'debug-type-' + log.type.toLowerCase()" v-text="log.type"></span>
                <span class="debug-direction" :class="'debug-direction-' + log.direction.toLowerCase()" v-text="log.direction"></span>
                <span v-if="log.url" class="debug-url" v-text="log.url"></span>
            </div>
            <pre class="debug-log-data" v-text="log.data"></pre>
        </div>
    </div>

    <!-- Conversation History Pane -->
    <div v-if="debugPaneMode === 'history'" class="debug-content">
        <h6>Conversation History</h6>
        <div class="debug-empty">
            No conversation history available yet.
        </div>
    </div>

    <!-- Tools Listing Pane -->
    <div v-if="debugPaneMode === 'tools'" class="debug-content">
        <h6>MCP Tools</h6>

        <!-- MCP Connection Status -->
        <div class="mcp-status-bar" style="margin-bottom: 10px; padding: 8px; border-radius: 4px;"
             :style="{ backgroundColor: mcpConnected ? '#d4edda' : '#f8d7da',
                       color: mcpConnected ? '#155724' : '#721c24',
                       border: mcpConnected ? '1px solid #c3e6cb' : '1px solid #f5c6cb' }">
            <i :class="mcpConnected ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'"></i>
            <span v-if="mcpLoading">Loading MCP tools...</span>
            <span v-else-if="mcpConnected">Connected to {{ mcpServerDisplay }} ({{ mcpTools.length }} tools)</span>
            <span v-else>Not connected to MCP server</span>
            <button @click="loadMcpTools"
                    class="btn btn-sm btn-outline-secondary"
                    style="float: right; margin-top: -2px;"
                    :disabled="mcpLoading">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': mcpLoading }"></i> Refresh
            </button>
        </div>

        <!-- Tools List -->
        <div v-if="mcpTools.length === 0 && !mcpLoading" class="debug-empty">
            No MCP tools available. Check server connection.
        </div>
        <div v-else-if="mcpTools.length > 0" class="mcp-tools-list">
            <div v-for="tool in mcpTools" :key="tool.name" class="mcp-tool-item"
                 style="margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;">
                <div class="tool-header" style="font-weight: bold; color: #333;">
                    <i class="fas fa-wrench" style="margin-right: 5px; color: #666;"></i>
                    {{ tool.name }}
                </div>
                <div class="tool-description" style="font-size: 0.9em; color: #666; margin-top: 4px;">
                    <span v-text="tool.description || 'No description available'"></span>
                </div>
                <div v-if="tool.input_schema && Object.keys(tool.input_schema).length > 0"
                     class="tool-schema" style="font-size: 0.8em; color: #888; margin-top: 4px;">
                    <details>
                        <summary style="cursor: pointer;">Parameters</summary>
                        <pre style="margin-top: 4px; font-size: 0.75em; background: #fff; padding: 4px; border-radius: 2px;" v-text="JSON.stringify(tool.input_schema, null, 2)"></pre>
                    </details>
                </div>
            </div>
        </div>
    </div>
</div>

