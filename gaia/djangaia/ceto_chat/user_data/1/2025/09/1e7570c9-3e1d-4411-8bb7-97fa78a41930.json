{"conversation_id": "1e7570c9-3e1d-4411-8bb7-97fa78a41930", "user_id": "1", "title": "Conversation 1e7570c9", "metadata": {}, "messages": [{"role": "user", "content": "hello", "timestamp": "2025-09-02T21:35:13.812920"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"hello\"", "timestamp": "2025-09-02T21:35:13.812925"}, {"role": "user", "content": "hello again", "timestamp": "2025-09-02T22:01:19.748047"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"hello again\"", "timestamp": "2025-09-02T22:01:19.748055"}, {"role": "user", "content": "hello", "timestamp": "2025-09-04T20:14:49.229647"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"hello\"", "timestamp": "2025-09-04T20:14:49.229652"}, {"role": "user", "content": "test1", "timestamp": "2025-09-04T20:54:10.087447"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"test1\"", "timestamp": "2025-09-04T20:54:10.087453"}, {"role": "user", "content": "test2", "timestamp": "2025-09-04T20:54:14.465175"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"test2\"", "timestamp": "2025-09-04T20:54:14.465184"}, {"role": "user", "content": "test1", "timestamp": "2025-09-04T22:51:16.055173"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"test1\"", "timestamp": "2025-09-04T22:51:16.055185"}, {"role": "user", "content": "test3", "timestamp": "2025-09-04T22:51:27.139324"}, {"role": "assistant", "content": "MCP Server available. This is the default message. \"test3\"", "timestamp": "2025-09-04T22:51:27.139330"}], "created_at": "2025-09-02T21:35:13.789784", "updated_at": "2025-09-04T22:51:27.139330"}