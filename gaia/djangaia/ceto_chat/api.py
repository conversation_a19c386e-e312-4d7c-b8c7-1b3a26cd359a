"""
Django API endpoints for ceto_chat conversation history using JSON conversation store.
No Django models are used; persistence relies on ceto_v002 ChatManager + ChronoStore.
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from django.http import JsonResponse, HttpRequest
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required

from gaia.gaia_ceto.ceto_v002.chatobj import ChatManager, Conversation

logger = logging.getLogger(__name__)


# Helpers

def _get_base_store_dir() -> str:
    """Return base directory for conversation storage, configurable via env var.
    Default to a per-app directory under ceto_chat/user_data.
    """
    env_dir = os.environ.get("CETO_CHAT_STORE_DIR")
    if env_dir:
        return env_dir
    # Default next to this file: gaia/djangaia/ceto_chat/user_data
    here = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(here, "user_data")


def _get_user_key(request: HttpRequest) -> str:
    """Use authenticated user id when available, otherwise fall back to query param.
    Endpoints are login_required, so request.user.id should generally exist.
    """
    if request.user and request.user.is_authenticated:
        return str(request.user.id)
    return request.GET.get("user", "anonymous")


def _make_manager(request: HttpRequest) -> ChatManager:
    """Instantiate ChatManager scoped to this user under base_dir/<user_key>."""
    base = _get_base_store_dir()
    user_key = _get_user_key(request)
    store_dir = os.path.join(base, user_key)
    os.makedirs(store_dir, exist_ok=True)
    return ChatManager(storage_dir=store_dir)


def _clean_title_from_first_message(first_message: Optional[str]) -> Optional[str]:
    if not first_message:
        return None
    words = first_message.strip().split()
    if not words:
        return None
    title = " ".join(words[:4])
    if len(words) > 4:
        title += "..."
    return title


# API endpoints

@login_required(login_url='/ceto_chat/login/')
@require_http_methods(["GET"])
def list_conversations(request: HttpRequest) -> JsonResponse:
    """Return the user's conversations (newest first)."""
    try:
        manager = _make_manager(request)
        user_key = _get_user_key(request)
        items = manager.list_conversations(user_id=user_key, limit=200, reverse=True)

        # Normalize/shape response
        results: List[Dict[str, Any]] = []
        for it in items:
            # Try to ensure updated_at; if missing, fallback to file mtime
            updated_at = it.get("updated_at")
            if not updated_at:
                try:
                    mtime = os.path.getmtime(it.get("file_path", ""))
                    updated_at = datetime.fromtimestamp(mtime).isoformat()
                except Exception:
                    updated_at = it.get("created_at")

            results.append({
                "id": it.get("conversation_id"),
                "title": it.get("title") or f"Conversation {str(it.get('conversation_id'))[:8]}",
                "created_at": it.get("created_at"),
                "updated_at": updated_at,
                "message_count": it.get("message_count", 0),
            })

        return JsonResponse({"success": True, "conversations": results})
    except Exception as e:
        logger.exception("Error listing conversations")
        return JsonResponse({"success": False, "error": str(e)}, status=500)


@login_required(login_url='/ceto_chat/login/')
@require_http_methods(["GET"])
def load_conversation(request: HttpRequest, conversation_id: str) -> JsonResponse:
    try:
        manager = _make_manager(request)
        conv = manager.load_conversation(conversation_id)
        if not conv:
            return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        messages = []
        for msg in conv.messages:
            messages.append({
                "role": msg.get("role"),
                "content": msg.get("content"),
                "timestamp": msg.get("timestamp", ""),
            })

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conv.conversation_id,
                "title": conv.title,
                "created_at": conv.created_at,
                "updated_at": conv.updated_at,
                "messages": messages,
            }
        })
    except Exception as e:
        logger.exception("Error loading conversation")
        return JsonResponse({"success": False, "error": str(e)}, status=500)


@login_required(login_url='/ceto_chat/login/')
@csrf_exempt
@require_http_methods(["POST"])
def create_conversation(request: HttpRequest) -> JsonResponse:
    try:
        payload = {}
        try:
            payload = json.loads(request.body.decode("utf-8")) if request.body else {}
        except Exception:
            payload = {}

        title = payload.get("title")
        first_message = payload.get("first_message")

        if not title:
            title = _clean_title_from_first_message(first_message)

        manager = _make_manager(request)
        user_key = _get_user_key(request)
        conv = manager.create_conversation(user_id=user_key, title=title)

        # Optionally add first user message
        if first_message and first_message.strip():
            manager.add_message("user", first_message.strip())

        # Persist conversation
        manager.save_conversation(conv)

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conv.conversation_id,
                "title": conv.title,
                "created_at": conv.created_at,
                "updated_at": conv.updated_at,
            }
        }, status=201)
    except Exception as e:
        logger.exception("Error creating conversation")
        return JsonResponse({"success": False, "error": str(e)}, status=500)


@login_required(login_url='/ceto_chat/login/')
@csrf_exempt
@require_http_methods(["DELETE"])
def delete_conversation(request: HttpRequest, conversation_id: str) -> JsonResponse:
    try:
        manager = _make_manager(request)
        ok = manager.delete_conversation(conversation_id)
        if not ok:
            return JsonResponse({"success": False, "error": "Delete failed or not found"}, status=404)
        return JsonResponse({"success": True})
    except Exception as e:
        logger.exception("Error deleting conversation")
        return JsonResponse({"success": False, "error": str(e)}, status=500)


@login_required(login_url='/ceto_chat/login/')
@csrf_exempt
@require_http_methods(["PATCH"])
def update_conversation(request: HttpRequest, conversation_id: str) -> JsonResponse:
    try:
        payload = {}
        try:
            payload = json.loads(request.body.decode("utf-8")) if request.body else {}
        except Exception:
            payload = {}

        new_title = payload.get("title")
        if new_title is None:
            return JsonResponse({"success": False, "error": "No fields to update"}, status=400)

        manager = _make_manager(request)
        conv = manager.load_conversation(conversation_id)
        if not conv:
            return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        conv.title = new_title.strip() or conv.title
        # Persist
        manager.save_conversation(conv)

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conv.conversation_id,
                "title": conv.title,
                "created_at": conv.created_at,
                "updated_at": conv.updated_at,
            }
        })
    except Exception as e:
        logger.exception("Error updating conversation")
        return JsonResponse({"success": False, "error": str(e)}, status=500)



@login_required(login_url='/ceto_chat/login/')
@csrf_exempt
@require_http_methods(["POST"])

def append_messages(request: HttpRequest, conversation_id: str) -> JsonResponse:
    """Append one or more messages to a conversation and persist immediately.

    Expected payload formats:
      - { "role": "user"|"assistant"|"system", "content": "...", "timestamp": "..." }
      - { "messages": [ {role, content, timestamp?}, ... ] }
    """
    try:
        payload = {}
        try:
            payload = json.loads(request.body.decode("utf-8")) if request.body else {}
        except Exception:
            payload = {}

        messages = payload.get("messages")
        # Support single message body
        if not messages and payload.get("role") and payload.get("content") is not None:
            messages = [ { "role": payload.get("role"), "content": payload.get("content"), "timestamp": payload.get("timestamp") } ]

        if not messages or not isinstance(messages, list):
            return JsonResponse({"success": False, "error": "No messages provided"}, status=400)

        manager = _make_manager(request)
        conv = manager.set_active_conversation(conversation_id)
        if not conv:
            return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        # Append messages to active conversation
        for m in messages:
            role = (m.get("role") or "user").strip()
            content = (m.get("content") or "").strip()
            if not content:
                continue
            manager.add_message(role, content)
        # Persist
        manager.save_conversation()

        # Return minimal updated info
        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conv.conversation_id,
                "title": conv.title,
                "created_at": conv.created_at,
                "updated_at": conv.updated_at,
                "message_count": len(conv.messages)
            }
        })
    except Exception as e:
        logger.exception("Error appending messages")
        return JsonResponse({"success": False, "error": str(e)}, status=500)
