// Chat provider abstraction (ESM)
// LiveProvider uses MCP HTTP endpoints; MockProvider uses local fixtures

import { checkMcpServer, loadMcpTools as liveLoadTools, callTool as liveCallTool, sendChat as liveSendChat } from './chat_mcp_utils.js';
import { CetoMockTools } from './mock_tools.js';
import { mockChat } from './mock_chat.js';

export const LiveProvider = {
  name: 'LiveProvider',
  async loadTools(ctx) { return await liveLoadTools(ctx); },
  async callTool(ctx, toolName, toolArgs = {}) { return await liveCallTool(ctx, toolName, toolArgs); },
  async sendChat(ctx, messageText) { return await liveSendChat(ctx, messageText); }
};

export const MockProvider = {
  name: 'MockProvider',
  async loadTools(ctx) {
    const tools = Object.entries(CetoMockTools || {}).map(([name, def]) => ({ name, description: def.description, input_schema: def.input_schema || {} }));
    ctx?.addDebugLog && ctx.addDebugLog('MCP', 'INFO', `Using mock tools fallback (${tools.length} tools)`);
    return { ok: true, tools, serverUrl: 'mock://local-fixture' };
  },
  async callTool(ctx, toolName, toolArgs = {}) {
    try {
      const tool = CetoMockTools && CetoMockTools[toolName];
      if (!tool || typeof tool.invoke !== 'function') return { ok: false, errorMessage: 'Mock tool not found' };
      const result = await tool.invoke(ctx, toolArgs);
      return { ok: !!result.success, result };
    } catch (e) {
      ctx?.addErrorLog && ctx.addErrorLog(`Mock tool error (${toolName}): ${e.message}`, 'MockProvider.callTool');
      return { ok: false, errorMessage: e.message };
    }
  },
  async sendChat(ctx, messageText) {
    const res = await mockChat(messageText, ctx);
    if (res && res.success) {
      return { ok: true, connected: false, mockUsed: true, assistantMessage: res.message, transcriptDelta: [ { role: 'user', content: messageText }, { role: 'assistant', content: res.message, mock_mode: true } ] };
    }
    return { ok: false, connected: false, mockUsed: true, errorMessage: 'Mock chat failed' };
  }
};

export let current = null;

export async function select(ctx) {
  try {
    const base = (typeof window !== 'undefined' && typeof window.MCP_API_BASE !== 'undefined') ? window.MCP_API_BASE : 'http://localhost:9000';
    const res = await checkMcpServer(ctx, base);
    if (res && res.ok) { current = LiveProvider; return LiveProvider; }
  } catch (e) {
    ctx?.addErrorLog && ctx.addErrorLog(`checkMcpServer error: ${e.message}`, 'ChatProviders.select');
  }
  current = MockProvider; return MockProvider;
}


export async function ensureProvider(ctx) {
  if (!current) await select(ctx);
  return current || MockProvider;
}

export async function initializeMcp(ctx) {
  const provider = await select(ctx);
  let res = { ok: false, tools: [], serverUrl: '', errorMessage: undefined };
  try {
    res = provider && provider.loadTools ? await provider.loadTools(ctx) : { ok: false };
  } catch (e) {
    res = { ok: false, tools: [], serverUrl: '', errorMessage: e.message };
  }
  return {
    ok: !!res.ok,
    providerName: provider?.name || 'Unknown',
    tools: res.tools || [],
    serverUrl: res.serverUrl || '',
    connected: provider?.name === 'LiveProvider',
    mock: provider?.name === 'MockProvider',
    errorMessage: res?.errorMessage
  };
}


const ChatProviders = { Live: LiveProvider, Mock: MockProvider, get current() { return current; }, select };
export default ChatProviders;

