// examples/progressive-enhancement.js
// Example showing how to use the port/adapter pattern for progressive enhancement

import { useChat } from '../core/app/useChat.js';
import { 
  SimpleChatPort, 
  MockChatPort, 
  LiveChatPort,
  SimpleRenderPort,
  VueRenderPort,
  CetoEffectsPort,
  createBarebones,
  createMock,
  createLive,
  createAuto
} from '../adapters/index.js';

// Example 1: Start with the absolute minimum
function exampleBarebones() {
  console.log('=== Example 1: Barebones Chat ===');
  
  const { chatPort, renderPort, effectsPort } = createBarebones();
  const chat = useChat(chatPort, renderPort, effectsPort);
  
  // This gives you a working echo chat with no dependencies
  console.log('Chat kernel created with simple echo functionality');
  return chat;
}

// Example 2: Add mock functionality (still offline)
function exampleMock() {
  console.log('=== Example 2: Mock Chat ===');
  
  const { chatPort, renderPort, effectsPort } = createMock();
  const chat = useChat(chatPort, renderPort, effectsPort);
  
  // Now you have mock tools and responses
  console.log('Chat kernel created with mock tools and responses');
  return chat;
}

// Example 3: Add live MCP (network-based)
function exampleLive() {
  console.log('=== Example 3: Live MCP Chat ===');
  
  const { chatPort, renderPort, effectsPort } = createLive('http://localhost:9000');
  const chat = useChat(chatPort, renderPort, effectsPort);
  
  // Now you have real MCP tools and LLM responses
  console.log('Chat kernel created with live MCP server connection');
  return chat;
}

// Example 4: Add enhanced rendering
function exampleEnhanced() {
  console.log('=== Example 4: Enhanced Rendering ===');
  
  const chatPort = new LiveChatPort('http://localhost:9000');
  const renderPort = new VueRenderPort();
  const effectsPort = null; // Still no effects
  
  const chat = useChat(chatPort, renderPort, effectsPort);
  
  // Now you have rich Vue component rendering
  console.log('Chat kernel created with enhanced Vue rendering');
  return chat;
}

// Example 5: Add full effects
function exampleFullEffects() {
  console.log('=== Example 5: Full Effects ===');
  
  const chatPort = new LiveChatPort('http://localhost:9000');
  const renderPort = new VueRenderPort();
  const effectsPort = new CetoEffectsPort({
    enableAnimations: true,
    enableScrolling: true
  });
  
  const chat = useChat(chatPort, renderPort, effectsPort);
  
  // Now you have animations, auto-scroll, notifications, etc.
  console.log('Chat kernel created with full effects');
  return chat;
}

// Example 6: Auto-detect best configuration
async function exampleAuto() {
  console.log('=== Example 6: Auto-Detection ===');
  
  const config = await createAuto({
    mcpApiBase: 'http://localhost:9000',
    effectsOptions: {
      enableAnimations: true,
      enableScrolling: true
    }
  });
  
  const chat = useChat(config.chatPort, config.renderPort, config.effectsPort);
  
  console.log(`Chat kernel created with auto-detected level: ${config.level}`);
  return { chat, config };
}

// Example 7: Custom mix-and-match
function exampleCustom() {
  console.log('=== Example 7: Custom Configuration ===');
  
  // Mix and match: Live chat with simple rendering and no effects
  const chatPort = new LiveChatPort('http://localhost:9000');
  const renderPort = new SimpleRenderPort(); // Simple instead of Vue
  const effectsPort = null; // No effects
  
  const chat = useChat(chatPort, renderPort, effectsPort);
  
  console.log('Chat kernel created with custom mix: Live + Simple + No Effects');
  return chat;
}

// Example 8: Testing with different configurations
async function exampleTesting() {
  console.log('=== Example 8: Testing Different Configurations ===');
  
  // Test with mock data
  const mockConfig = createMock();
  const mockChat = useChat(mockConfig.chatPort, mockConfig.renderPort, mockConfig.effectsPort);
  
  // Send a test message
  await mockChat.send('Hello, test!');
  console.log('Mock messages:', mockChat.messages);
  
  // Test tool call
  await mockChat.send('/web_search test query');
  console.log('Mock timeline:', mockChat.timeline);
  
  return mockChat;
}

// Example usage in a Vue component
function exampleVueComponent() {
  return {
    name: 'ExampleChatComponent',
    setup() {
      const chatKernel = ref(null);
      
      onMounted(async () => {
        // Start with auto-detection
        const config = await createAuto();
        chatKernel.value = useChat(config.chatPort, config.renderPort, config.effectsPort);
        
        // Load tools
        await chatKernel.value.loadTools();
      });
      
      return {
        chatKernel,
        async sendMessage(text) {
          if (chatKernel.value) {
            await chatKernel.value.send(text);
          }
        }
      };
    },
    template: `
      <div v-if="chatKernel">
        <div v-for="item in chatKernel.renderItems" :key="item.id">
          {{ item.displayText || item.text }}
        </div>
        <input @keyup.enter="sendMessage($event.target.value)" placeholder="Type a message...">
      </div>
    `
  };
}

// Export examples for testing
export {
  exampleBarebones,
  exampleMock,
  exampleLive,
  exampleEnhanced,
  exampleFullEffects,
  exampleAuto,
  exampleCustom,
  exampleTesting,
  exampleVueComponent
};

// Run examples if this file is loaded directly
if (typeof window !== 'undefined' && window.location.search.includes('run-examples')) {
  console.log('Running progressive enhancement examples...');
  
  // Run synchronous examples
  exampleBarebones();
  exampleMock();
  exampleLive();
  exampleEnhanced();
  exampleFullEffects();
  exampleCustom();
  
  // Run async examples
  exampleAuto().then(result => {
    console.log('Auto example completed:', result);
  });
  
  exampleTesting().then(result => {
    console.log('Testing example completed:', result);
  });
}
