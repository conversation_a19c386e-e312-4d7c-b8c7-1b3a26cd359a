# Ceto Chat Frontend (ESM) – How the JS modules interact

This document explains the rationale and architecture of the Ceto Chat frontend that lives under `gaia/djangaia/ceto_chat/static/ceto_chat/`. It focuses on:
- Why we use native ES Modules (ESM)
- Which files exist and how they depend on each other
- How Dependency Inversion is respected in the design
- How to safely add new components and modules without breaking dependency loading


## Why ESM (and why now)

We intentionally use the browser’s native ES Module system instead of a bundler:
- Simpler: No build step or tooling required. Django serves the static files as-is.
- Explicit graph: Dependencies are declared via `import` / `export` so the module graph is easy to reason about.
- Better isolation: Each file has its own scope; no accidental globals.
- Progressive migration: We can migrate incrementally from legacy globals to ESM.

Important: ESM files must be loaded either via a `<script type="module" ...>` entrypoint or imported from other ESM modules. Do not include ESM files using classic `<script src>` tags (that will cause “Unexpected token export”).



## Directory map (static/ceto_chat)

```
/static/ceto_chat/
  ceto_app.module.js            // ESM entrypoint (mount-only)
  ceto_chat.css                 // shared styles
  chat_providers.js             // provider selector + helpers (initializeMcp, ensureProvider)
  chat_mcp_utils.js             // HTTP helpers for MCP (tools, call, chat)
  chat_response.module.js       // tool/chat response renderer component
  conversations_panel.module.js // sidebar UI for conversations
  debug.js                      // DebugPanelComponent (+ legacy mixin; app now uses composable)
  mock_tools.js                 // local mock tools
  mock_chat.js                  // local mock chat
  services/
    api.js                      // fetchJsonOrError wrapper
  ui_flourishes.js              // optional UI listeners (no side effects if omitted)
  utils/
    events.js                   // chat event bus (emitChatEvent)
    markdown.js                 // markdown rendering helper
  core/
    app/
      useChat.js                // DI kernel used by root app
    ui/
      ChatWindow.module.js      // root orchestrator (registers components, uses composables)
    composables/
      useConversations.js       // replaces ConversationsMixin
      useDebug.js               // replaces DebugPanelMixin
  dist/
    vue.esm-browser.js          // pinned Vue ESM (local fallback)
```

## How files are loaded

You can run the frontend either:
- Standalone static HTML at `gaia/gaia_ceto_v2/frontend/chat_response_with_tools.html` (works with Python SimpleHTTPServer or any static host), or
- A Django template that includes the same tags (if present in your project)

Both variants:
- Define an `importmap` for Vue:
  ```html
  <script type="importmap">{"imports": {"vue": "https://unpkg.com/vue@3.4.38/dist/vue.esm-browser.js"}}</script>
  ```
- Load the single ESM entrypoint:
  ```html
  <script type="module" src="/gaia/djangaia/ceto_chat/static/ceto_chat/ceto_app.module.js"></script>
  ```
- Set `window.MCP_API_BASE` to point to the MCP HTTP server (e.g., `http://127.0.0.1:9000`).

From there:
- `ceto_app.module.js` now only mounts the root component (mount-only entrypoint).
- `ChatWindow.module.js` registers UI components, uses composables (useConversations, useDebug), and performs provider calls via chat_providers.js.
- All other files are pulled in via ESM imports from these modules.


## Module map and responsibilities

Top-level entry
- `ceto_app.module.js` (ESM)
  - Mounts the Vue app with `createApp(ChatWindow).mount('#app')`.

UI components
- `chat_response.module.js` (ESM)
  - Unified response renderer (both chat and tool). Register/use as `chat-response-panel` with `:response`.
- `conversations_panel.module.js` (ESM)
  - Sidebar component for listing/selecting/deleting conversations (click row to open; delete button stops click propagation).
- `debug.js` (ESM)
  - Exports `DebugPanelComponent` (legacy mixin remains for backwards compatibility).

Composables and utilities
- `core/composables/useConversations.js`
  - Replaces ConversationsMixin; same behavior (load/create/select/delete/ensure/append) but explicit and testable.
- `core/composables/useDebug.js`
  - Replaces DebugPanelMixin; same debug state and methods.
- `chat_mcp_utils.js` (ESM)
  - Utility helpers to talk to the MCP HTTP endpoints (load tools, call tool, send chat).
  - Uses `window.MCP_API_BASE` and falls back to mock behavior if the MCP server fails.

Provider abstraction
- `chat_providers.js` (ESM)
  - `initializeMcp(ctx)` returns providerName, tools, serverUrl, connected, mock, ok, errorMessage.
  - `ensureProvider(ctx)` returns the current provider object exposing `callTool()` and `sendChat()`.

Root component
- `core/ui/ChatWindow.module.js`
  - Registers components locally, wires composables in `created()`, provides chatKernel for injection.
  - Owns app state (messages, timeline, toolResponses, MCP status) and orchestration (handleSendUnified, callTool, sendChat, loadMcpTools, renderItems helpers).

Providers and mocks (Dependency Inversion)
- `chat_providers.js` (ESM)
  - Defines two providers with the same interface:
    - `LiveProvider`: delegates to `chat_mcp_utils.js` for real MCP calls
    - `MockProvider`: uses local fixtures in `mock_tools.js` and `mock_chat.js`
  - Exports `select(ctx)` which picks a provider at runtime based on connectivity.
  - New: `initializeMcp(ctx)` to load tools and return connection/tool state, `ensureProvider(ctx)` to retrieve the current provider for calls.
  - The UI depends only on this abstraction, not on concrete implementations.
- `mock_tools.js` (ESM)
  - Exports `CetoMockTools` object. Each tool exposes an `invoke(ctx, toolArgs)` that returns a mocked JSON-RPC-like result.
- `mock_chat.js` (ESM)
  - Exports `mockChat(message, ctx)` which returns a canned chat response. Used when MCP is unavailable.

Styling
- `ceto_chat.css`
  - Contains layout for the two-column UI, the scrollable transcript area, and the sticky input bar.


## Dependency Inversion in practice

- The app (UI layer) depends on the `Provider` interface, not on the concrete MCP or mock implementations.
  - `ceto_app.module.js` imports `select()` from `chat_providers.js` and then calls the provider’s `loadTools`, `callTool`, and `sendChat` methods.
  - This means the UI is unaware of how tools or chat are fulfilled (live HTTP vs mock fixtures).
- Lower-level utilities (`chat_mcp_utils.js`) never import UI components. They are pure logic modules receiving context from the caller (`ctx`) for logging and error handling.
- Mock implementations are plug-compatible and live under the provider layer.
- The conversations system (`conversations.js`) exposes a mixin the UI can adopt, but it communicates strictly via REST endpoints; it doesn’t reach into the provider internals.

This design enables swapping providers, adding new backends, or testing with mocks without changing the UI code that renders lists, inputs, and transcripts.


## Runtime flow (high level)

1. Template loads `ceto_app.module.js` with `<script type="module">` and defines `window.MCP_API_BASE`.
2. Entry mounts `ChatWindow`.
3. ChatWindow `mounted()` calls `loadMcpTools()` and `loadConversations()`.
4. Conversations sidebar uses useConversations; clicking a conversation loads it, updates the URL, and populates `messages`.
5. Center pane shows transcript for `raw_chat` and sticky input.
6. On send, the provider’s `sendChat()` is used. After a successful response, the app calls `appendTranscriptDelta(...)` to persist messages. If no conversation exists, `ensureConversationId()` creates one and updates the URL.


## Module graph (Mermaid)

```mermaid
flowchart TD
  A[ceto_chat_base.html] -- type="module" --> B[ceto_app.module.js]
  B --> C[debug.js\n(DebugPanelMixin, DebugPanelComponent)]
  B --> D[conversations.js\n(ConversationsMixin)]
  B --> E[chat_response.module.js\n(also exports ToolResponsePanel)]
  B --> G[conversations_panel.module.js]

  B -. dynamic import .-> H[chat_providers.js]
  H --> I[LiveProvider]
  H --> J[MockProvider]
  I --> K[chat_mcp_utils.js]
  J --> L[mock_tools.js\n(CetoMockTools)]
  J --> M[mock_chat.js]

  subgraph UI Components
    E
    F
    G
  end

  subgraph Providers & Utils
    H
    I
    J
    K
    L
    M
  end

  K -.->|HTTP| MCP[MCP HTTP Server]
```

This diagram shows:
- The template loads only the ESM entrypoint.
- The entrypoint imports core UI/mixins and dynamically chooses a provider.
- Live provider calls MCP via utilities; Mock provider uses local mocks.
- UI depends on provider abstraction, not on concrete implementations.


## Adding new components safely

Follow these guidelines to avoid breaking dependency loading:

1) Use ESM and default exports for components
- Create a file `my_widget.module.js`:
  ```js
  export default {
    name: 'MyWidget',
    props: { /* ... */ },
    template: `<div class="my-widget">Hello from MyWidget</div>`
  };
  ```
- Register it in the app (eager import):
  ```js
  import MyWidget from './my_widget.module.js';
  app.component('my-widget', MyWidget);
  ```
- Or register it lazily where used:
  ```js
  const MyWidget = (await import('./my_widget.module.js')).default;
  app.component('my-widget', MyWidget);
  ```

2) Keep directions of dependency clean
- UI components can import mixins/utilities, not vice‑versa.
- Providers can import utilities and mocks, not UI.
- Utilities should be side‑effect free and avoid touching the DOM.

3) Avoid globals and classic `<script>` tags for modules
- Do not add `<script src="...my_es_module.js">` without `type="module"`.
- Let `ceto_app.module.js` import new modules instead of adding extra tags to HTML.

4) Respect provider abstraction
- If you need new chat behavior (e.g., streaming, another backend), add a new provider with the same shape:
  ```js
  export const MyProvider = {
    name: 'MyProvider',
    async loadTools(ctx) { /* ... */ },
    async callTool(ctx, toolName, toolArgs) { /* ... */ },
    async sendChat(ctx, messageText) { /* ... */ }
  };
  ```
- Update `chat_providers.js` to export it and extend `select(ctx)` logic if needed.

5) Add utilities as pure functions
- Put reusable logic into `*.js` files that export functions/classes.
- Accept all external services via arguments (URLs, tokens, ctx) instead of importing app state.

6) Prevent circular imports
- Components should not import each other cyclically. If two parts need to talk, factor a shared utility or an event/method on the parent app.
- If you sense a potential cycle, prefer dynamic `import()` at the leaf call site.

7) Keep templates small and focused
- If a component grows large, extract subsections into their own `*.module.js` components and register them under a namespace (e.g., `mcp-...`).

8) Verify in the browser
- Hard refresh to bust caches after changing import graphs.
- Watch the console for ESM and Vue warnings (unresolved specifiers, v-if/v-else adjacency, etc.).
- Use Network panel to ensure modules load with status 200.


## Quick reference: key files

- Entry: `ceto_app.module.js`
- Providers: `chat_providers.js` (selects `LiveProvider` vs `MockProvider`)
- MCP utilities: `chat_mcp_utils.js`
- UI renderers: `chat_response.module.js`
- Mocks: `mock_tools.js`, `mock_chat.js`
- Conversations: `conversations.js` (mixin), `conversations_panel.module.js` (component)
- Debug UI: `debug.js` (mixin + component)
- Styles: `ceto_chat.css`

With this structure, you can add new UI components, backends, or utilities without changing how the app boots or how dependencies are wired.




## Unified execution model (chat + slash tools)

The single input supports both plain chat and slash-invoked tool calls.

- Plain chat (no leading `/`)
  - Flow: sendChat -> provider.sendChat -> MCP `/chat/message` (or mock) -> UI appends user + assistant to `messages`.
  - Persistence: After a successful send, the UI POSTs to `POST /ceto_chat/api/conversations/<id>/append/` with the user and assistant messages.
  - If no conversation exists yet, the app will create one first and update the URL with `?conversation_id=<id>`.

- Slash tools (text starts with `/`)
  - Flow: handleSendUnified detects the `/tool args` form, inserts a pending tool event into the in-memory `timeline`, and calls the current provider’s `callTool`.
  - Persistence: On success, the UI persists two messages via the same append endpoint:
    1) `{ role: 'user', content: '/<tool> <args>' }` – the literal slash command the user typed
    2) `{ role: 'tool', content: JSON.stringify({ tool: '<tool>', rpc: <jsonrpc-like result> }) }`
  - No chat endpoint is called for slash tools; only the tool-call endpoint (MCP) and the conversation append API are used.

This means that after a reload, tool results re-hydrate from the persisted "tool" message rather than from any in-memory state.


## Rendering and ordering

- The center pane renders a merged list `renderItems` composed of:
  - `messages` (persisted history from the server)
  - `timeline` (ephemeral, in-session events like pending tool calls)
- On load, any persisted message with `role: 'tool'` is parsed and displayed using the same tool panel renderer used live, so it looks identical to the pre-refresh state.
- Timestamps from Django often include microseconds. The frontend normalizes these to milliseconds before parsing to ensure strict chronological ordering; new messages appear below earlier ones without requiring a reload.


## Persistence schema for tool results

- Tool results are stored as a message with `role: 'tool'` and a JSON string in `content`:
  ```json
  {
    "role": "tool",
    "content": "{\"tool\": \"echostring\", \"rpc\": { /* JSON-RPC-like result */ }}"
  }
  ```
- The renderer looks for either `content.tool`/`content.tool_name` and `content.rpc` and shows the tool panel accordingly.
- This approach avoids hardcoding tool lists in the UI and preserves the exact response payload.


## Notes and limitations
- If you change the persisted JSON shape, update the reconstruction code in `ceto_app.module.js` accordingly.
