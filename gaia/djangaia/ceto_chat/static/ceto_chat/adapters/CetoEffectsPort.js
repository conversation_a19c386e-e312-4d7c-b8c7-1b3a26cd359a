// CetoEffectsPort.js
// EffectsPort adapter for Ceto chat effects and side effects
// Handles animations, notifications, scrolling, and event emissions

// Helper function to emit custom events (matches existing pattern)
function emitChatEvent(eventName, detail = {}) {
  if (typeof window !== 'undefined' && window.dispatchEvent) {
    const event = new CustomEvent(`ceto-chat-${eventName}`, { 
      detail,
      bubbles: true,
      cancelable: true 
    });
    window.dispatchEvent(event);
  }
}

export class CetoEffectsPort {
  constructor(options = {}) {
    this.eventEmitter = options.eventEmitter;
    this.scrollHandler = options.scrollHandler;
    this.debugLogger = options.debugLogger;
    this.errorLogger = options.errorLogger;
    this.enableAnimations = options.enableAnimations !== false; // default true
    this.enableScrolling = options.enableScrolling !== false; // default true
  }
  
  onSending() {
    // Emit custom event for external listeners
    emitChatEvent('send-start');
    
    // Call custom event emitter if provided
    this.eventEmitter?.emit?.('send-start');
    
    // Log debug info
    this.debugLogger?.addDebugLog?.('CHAT', 'SENDING', 'Message send started');
  }
  
  onReceivePending() {
    emitChatEvent('receive-pending');
    this.eventEmitter?.emit?.('receive-pending');
    this.debugLogger?.addDebugLog?.('CHAT', 'PENDING', 'Waiting for response');
  }
  
  onChatResponse(response) {
    emitChatEvent('chat-response', { response });
    this.eventEmitter?.emit?.('chat-response', response);
    
    // Auto-scroll after chat response
    if (this.enableScrolling) {
      this.requestScroll();
    }
    
    this.debugLogger?.addDebugLog?.('CHAT', 'RECEIVED', 
      `Response received (mock: ${response.mockUsed})`);
  }
  
  onToolResult(result) {
    emitChatEvent('tool-result', { result });
    this.eventEmitter?.emit?.('tool-result', result);
    
    // Auto-scroll after tool result
    if (this.enableScrolling) {
      this.requestScroll();
    }
    
    this.debugLogger?.addDebugLog?.('TOOL', 'RESULT', 
      `Tool result received: ${result?.jsonrpc ? 'success' : 'error'}`);
  }
  
  onDelivered(items) {
    emitChatEvent('delivered', { items });
    this.eventEmitter?.emit?.('delivered', items);
    
    // Final scroll after delivery
    if (this.enableScrolling) {
      this.requestScroll();
    }
  }
  
  onError(error) {
    emitChatEvent('error', { error: error.message });
    this.eventEmitter?.emit?.('error', error);
    
    // Log error
    this.errorLogger?.addErrorLog?.(error.message, 'CetoEffectsPort');
    
    console.error('Chat error:', error);
  }
  
  onLoadingTools() {
    emitChatEvent('loading-tools');
    this.eventEmitter?.emit?.('loading-tools');
    this.debugLogger?.addDebugLog?.('TOOLS', 'LOADING', 'Loading MCP tools');
  }
  
  onToolsLoaded(result) {
    emitChatEvent('tools-loaded', { result });
    this.eventEmitter?.emit?.('tools-loaded', result);
    
    const toolCount = result?.tools?.length || 0;
    this.debugLogger?.addDebugLog?.('TOOLS', 'LOADED', 
      `${toolCount} tools loaded (mock: ${result.mockUsed})`);
  }
  
  onTranscriptDelta(delta) {
    emitChatEvent('transcript-delta', { delta });
    this.eventEmitter?.emit?.('transcript-delta', delta);
  }
  
  onScrollRequest() {
    if (this.enableScrolling) {
      this.requestScroll();
    }
  }
  
  // Private helper for scroll handling
  requestScroll() {
    if (this.scrollHandler?.scrollTranscriptToBottom) {
      // Use provided scroll handler
      this.scrollHandler.scrollTranscriptToBottom();
    } else {
      // Fallback to DOM-based scrolling
      this.scrollToBottom();
    }
  }
  
  // Fallback scroll implementation
  scrollToBottom() {
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      // Try to find chat transcript element
      const transcript = document.getElementById('chat-transcript') || 
                        document.querySelector('.chat-transcript') ||
                        document.querySelector('[data-chat-transcript]');
      
      if (transcript) {
        // Use requestAnimationFrame for smooth scrolling
        requestAnimationFrame(() => {
          transcript.scrollTop = transcript.scrollHeight;
        });
      }
    }
  }
  
  // Configuration methods
  setScrollingEnabled(enabled) {
    this.enableScrolling = enabled;
  }
  
  setAnimationsEnabled(enabled) {
    this.enableAnimations = enabled;
  }
  
  // Method to add custom event listeners
  addEventListener(eventName, handler) {
    if (typeof window !== 'undefined') {
      window.addEventListener(`ceto-chat-${eventName}`, handler);
    }
  }
  
  removeEventListener(eventName, handler) {
    if (typeof window !== 'undefined') {
      window.removeEventListener(`ceto-chat-${eventName}`, handler);
    }
  }
}
