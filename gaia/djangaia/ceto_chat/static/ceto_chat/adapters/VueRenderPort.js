// VueRenderPort.js
// RenderPort adapter for Vue.js component rendering
// Handles conversion of chat/tool data into renderable items

export class VueRenderPort {
  constructor(componentRegistry = null) {
    this.componentRegistry = componentRegistry || new Map();
  }
  
  resolve(item) {
    if (item.component && this.componentRegistry.has(item.component)) {
      return this.componentRegistry.get(item.component);
    }
    return item;
  }
  
  renderChat(message) {
    const role = (message.role || '').toLowerCase();
    
    return {
      id: message.id || `chat-${Date.now()}-${Math.random()}`,
      kind: 'chat',
      role: role,
      text: message.content || message.text || '',
      timestamp: message.ts || Date.now(),
      mockMode: message.mock_mode || false,
      component: 'chat-message',
      // Additional metadata for rendering
      metadata: {
        isUser: role === 'user',
        isAssistant: role === 'assistant',
        isSystem: role === 'system',
        isMock: message.mock_mode || false
      }
    };
  }
  
  renderTool(toolEvent) {
    return {
      id: toolEvent.id || `tool-${Date.now()}-${Math.random()}`,
      kind: 'tool',
      toolName: toolEvent.toolName,
      argsText: toolEvent.argsText || '',
      result: toolEvent.result,
      status: toolEvent.status || 'complete',
      timestamp: toolEvent.ts || Date.now(),
      component: 'tool-response',
      // Additional metadata for rendering
      metadata: {
        isPending: toolEvent.status === 'pending',
        isComplete: toolEvent.status === 'complete',
        isError: toolEvent.status === 'error',
        hasResult: !!toolEvent.result
      }
    };
  }
  
  renderTimeline(items) {
    // Sort by timestamp for chronological order
    return items.sort((a, b) => {
      const aTime = a.timestamp || 0;
      const bTime = b.timestamp || 0;
      return aTime - bTime;
    });
  }
  
  // Helper method to register components
  registerComponent(name, component) {
    this.componentRegistry.set(name, component);
  }
  
  // Helper method to get component registry
  getComponentRegistry() {
    return this.componentRegistry;
  }
  
  // Method to create render items with comparison function
  // (matches the existing compareRenderItems logic)
  compareRenderItems(a, b) {
    const aTime = a.timestamp || 0;
    const bTime = b.timestamp || 0;
    
    if (aTime !== bTime) {
      return aTime - bTime;
    }
    
    // If timestamps are equal, maintain stable sort by id
    const aId = a.id || '';
    const bId = b.id || '';
    return aId.localeCompare(bId);
  }
}
