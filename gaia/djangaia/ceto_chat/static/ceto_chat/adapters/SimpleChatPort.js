// SimpleChatPort.js
// Minimal ChatPort implementation for barebones chat functionality
// Perfect for getting started without any external dependencies

export class SimpleChatPort {
  constructor(options = {}) {
    this.name = 'SimpleChatPort';
    this.echoMode = options.echoMode !== false; // default true
    this.responses = options.responses || [
      "I'm a simple chat bot. I can echo your messages!",
      "That's interesting! Tell me more.",
      "I understand. How can I help you with that?",
      "Thanks for sharing that with me.",
      "I see what you mean. What would you like to do next?"
    ];
    this.responseIndex = 0;
  }
  
  async loadTools(ctx) {
    // No tools available in simple mode
    return { 
      ok: true, 
      tools: [], 
      connected: false, 
      mockUsed: true,
      message: 'Simple chat mode - no tools available'
    };
  }
  
  async callTool(ctx, toolName, toolArgs) {
    // No tools available
    return { 
      jsonrpc: '2.0', 
      error: { 
        code: -1, 
        message: `Simple chat mode does not support tools. Tool '${toolName}' is not available.` 
      } 
    };
  }
  
  async sendChat(ctx, messageText) {
    // Simulate a small delay for realism
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    let responseMessage;
    
    if (this.echoMode) {
      responseMessage = `Echo: ${messageText}`;
    } else {
      // Use rotating responses
      responseMessage = this.responses[this.responseIndex % this.responses.length];
      this.responseIndex++;
    }
    
    return {
      ok: true,
      connected: false,
      mockUsed: true,
      assistantMessage: responseMessage,
      transcriptDelta: [
        { role: 'user', content: messageText },
        { role: 'assistant', content: responseMessage, mock_mode: true }
      ]
    };
  }
  
  async isAvailable() {
    // Simple chat is always available
    return true;
  }
  
  // Configuration methods
  setEchoMode(enabled) {
    this.echoMode = enabled;
  }
  
  addResponse(response) {
    this.responses.push(response);
  }
  
  setResponses(responses) {
    this.responses = responses;
    this.responseIndex = 0;
  }
  
  getResponses() {
    return [...this.responses];
  }
}
