// PortFactory.js
// Factory for creating and configuring ports for progressive enhancement
// Provides easy setup for different levels of functionality

import { SimpleChatPort } from './SimpleChatPort.js';
import { MockChatPort } from './MockChatPort.js';
import { LiveChatPort } from './LiveChatPort.js';
import { SimpleRenderPort } from './SimpleRenderPort.js';
import { VueRenderPort } from './VueRenderPort.js';
import { CetoEffectsPort } from './CetoEffectsPort.js';

export class PortFactory {
  constructor() {
    this.defaultMcpApiBase = 'http://localhost:9000';
  }
  
  // Progressive enhancement levels
  
  // Level 1: Barebones chat (no dependencies)
  createBarebones() {
    return {
      chatPort: new SimpleChatPort(),
      renderPort: new SimpleRenderPort(),
      effectsPort: null
    };
  }
  
  // Level 2: Mock functionality (offline capable)
  createMock() {
    return {
      chatPort: new MockChatPort(),
      renderPort: new SimpleRenderPort(),
      effectsPort: null
    };
  }
  
  // Level 3: Live MCP (network-based)
  createLive(mcpApiBase = null) {
    return {
      chatPort: new LiveChatPort(mcpApiBase || this.defaultMcpApiBase),
      renderPort: new SimpleRenderPort(),
      effectsPort: null
    };
  }
  
  // Level 4: Enhanced rendering (Vue components)
  createEnhancedRender(chatPortType = 'live', mcpApiBase = null) {
    let chatPort;
    
    switch (chatPortType) {
      case 'simple':
        chatPort = new SimpleChatPort();
        break;
      case 'mock':
        chatPort = new MockChatPort();
        break;
      case 'live':
      default:
        chatPort = new LiveChatPort(mcpApiBase || this.defaultMcpApiBase);
        break;
    }
    
    return {
      chatPort,
      renderPort: new VueRenderPort(),
      effectsPort: null
    };
  }
  
  // Level 5: Full effects (animations, scrolling, etc.)
  createFullEffects(chatPortType = 'live', mcpApiBase = null, effectsOptions = {}) {
    let chatPort;
    
    switch (chatPortType) {
      case 'simple':
        chatPort = new SimpleChatPort();
        break;
      case 'mock':
        chatPort = new MockChatPort();
        break;
      case 'live':
      default:
        chatPort = new LiveChatPort(mcpApiBase || this.defaultMcpApiBase);
        break;
    }
    
    return {
      chatPort,
      renderPort: new VueRenderPort(),
      effectsPort: new CetoEffectsPort(effectsOptions)
    };
  }
  
  // Auto-detect best available configuration
  async createAuto(options = {}) {
    const mcpApiBase = options.mcpApiBase || this.defaultMcpApiBase;
    const effectsOptions = options.effectsOptions || {};

    console.log('🏭 PortFactory.createAuto starting with:', { mcpApiBase, effectsOptions });

    // Try live first
    const liveChatPort = new LiveChatPort(mcpApiBase);

    try {
      console.log('🔍 Testing live chat port availability...');
      const isLiveAvailable = await liveChatPort.isAvailable();
      console.log('📊 Live port availability result:', isLiveAvailable);

      if (isLiveAvailable) {
        // Live MCP is available - use full setup
        console.log('✅ Using live configuration');
        return {
          chatPort: liveChatPort,
          renderPort: new VueRenderPort(),
          effectsPort: new CetoEffectsPort(effectsOptions),
          level: 'live'
        };
      }
    } catch (error) {
      console.warn('❌ Live MCP not available, falling back to mock:', error.message);
    }

    // Fallback to mock
    console.log('🔄 Falling back to mock configuration');
    return {
      chatPort: new MockChatPort(),
      renderPort: new VueRenderPort(),
      effectsPort: new CetoEffectsPort(effectsOptions),
      level: 'mock'
    };
  }
  
  // Custom configuration
  createCustom(chatPort, renderPort, effectsPort) {
    return {
      chatPort,
      renderPort,
      effectsPort
    };
  }
  
  // Utility methods
  setDefaultMcpApiBase(apiBase) {
    this.defaultMcpApiBase = apiBase;
  }
  
  getDefaultMcpApiBase() {
    return this.defaultMcpApiBase;
  }
  
  // Create specific port types
  createChatPort(type, options = {}) {
    switch (type) {
      case 'simple':
        return new SimpleChatPort(options);
      case 'mock':
        return new MockChatPort();
      case 'live':
        return new LiveChatPort(options.mcpApiBase || this.defaultMcpApiBase);
      default:
        throw new Error(`Unknown chat port type: ${type}`);
    }
  }
  
  createRenderPort(type, options = {}) {
    switch (type) {
      case 'simple':
        return new SimpleRenderPort();
      case 'vue':
        return new VueRenderPort(options.componentRegistry);
      default:
        throw new Error(`Unknown render port type: ${type}`);
    }
  }
  
  createEffectsPort(options = {}) {
    return new CetoEffectsPort(options);
  }
}

// Export singleton instance for convenience
export const portFactory = new PortFactory();
