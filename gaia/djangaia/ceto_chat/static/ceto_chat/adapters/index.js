// adapters/index.js
// Barrel export for all port adapters

// Chat Ports
export { SimpleChatPort } from './SimpleChatPort.js';
export { MockChatPort } from './MockChatPort.js';
export { LiveChatPort } from './LiveChatPort.js';

// Render Ports
export { SimpleRenderPort } from './SimpleRenderPort.js';
export { VueRenderPort } from './VueRenderPort.js';

// Effects Ports
export { CetoEffectsPort } from './CetoEffectsPort.js';

// Factory
export { PortFactory, portFactory } from './PortFactory.js';

// Convenience functions for quick setup
export function createBarebones() {
  return portFactory.createBarebones();
}

export function createMock() {
  return portFactory.createMock();
}

export function createLive(mcpApiBase) {
  return portFactory.createLive(mcpApiBase);
}

export function createEnhanced(chatPortType = 'live', mcpApiBase) {
  return portFactory.createEnhancedRender(chatPortType, mcpApiBase);
}

export function createFull(chatPortType = 'live', mcpApiBase, effectsOptions) {
  return portFactory.createFullEffects(chatPortType, mcpApiBase, effectsOptions);
}

export async function createAuto(options) {
  return await portFactory.createAuto(options);
}
