// SimpleRenderPort.js
// Minimal RenderPort implementation for basic text rendering
// Perfect for getting started without complex UI components

export class SimpleRenderPort {
  constructor() {
    this.name = 'SimpleRenderPort';
  }
  
  resolve(item) {
    // Simple resolution - just return the item as-is
    return item;
  }
  
  renderChat(message) {
    const role = (message.role || '').toLowerCase();
    
    return {
      id: message.id || `chat-${Date.now()}-${Math.random()}`,
      kind: 'chat',
      role: role,
      text: message.content || message.text || '',
      timestamp: message.ts || Date.now(),
      mockMode: message.mock_mode || false,
      // Simple rendering metadata
      displayText: this.formatChatMessage(role, message.content || message.text || ''),
      cssClass: this.getChatCssClass(role, message.mock_mode)
    };
  }
  
  renderTool(toolEvent) {
    return {
      id: toolEvent.id || `tool-${Date.now()}-${Math.random()}`,
      kind: 'tool',
      toolName: toolEvent.toolName,
      argsText: toolEvent.argsText || '',
      result: toolEvent.result,
      status: toolEvent.status || 'complete',
      timestamp: toolEvent.ts || Date.now(),
      // Simple rendering metadata
      displayText: this.formatToolResult(toolEvent),
      cssClass: this.getToolCssClass(toolEvent.status)
    };
  }
  
  renderTimeline(items) {
    // Sort by timestamp for chronological order
    return items.sort((a, b) => {
      const aTime = a.timestamp || 0;
      const bTime = b.timestamp || 0;
      return aTime - bTime;
    });
  }
  
  // Helper methods for simple formatting
  formatChatMessage(role, text) {
    const roleLabel = role.charAt(0).toUpperCase() + role.slice(1);
    return `${roleLabel}: ${text}`;
  }
  
  formatToolResult(toolEvent) {
    if (toolEvent.status === 'pending') {
      return `Tool: ${toolEvent.toolName} — running…`;
    }
    
    if (toolEvent.status === 'error') {
      const errorMsg = toolEvent.result?.error?.message || 'Tool execution failed';
      return `Tool: ${toolEvent.toolName} — Error: ${errorMsg}`;
    }
    
    // Try to format the result nicely
    let resultText = 'No result';
    if (toolEvent.result) {
      if (typeof toolEvent.result === 'string') {
        resultText = toolEvent.result;
      } else if (toolEvent.result.result) {
        resultText = typeof toolEvent.result.result === 'string' 
          ? toolEvent.result.result 
          : JSON.stringify(toolEvent.result.result, null, 2);
      } else {
        resultText = JSON.stringify(toolEvent.result, null, 2);
      }
    }
    
    return `Tool: ${toolEvent.toolName} — ${resultText}`;
  }
  
  getChatCssClass(role, mockMode) {
    const classes = ['message-item'];
    
    if (role === 'user') {
      classes.push('message-user');
    } else if (role === 'assistant') {
      classes.push('message-assistant');
    } else if (role === 'system') {
      classes.push('message-system');
    }
    
    if (mockMode) {
      classes.push('message-mock');
    }
    
    return classes.join(' ');
  }
  
  getToolCssClass(status) {
    const classes = ['tool-item'];
    
    if (status === 'pending') {
      classes.push('tool-pending');
    } else if (status === 'error') {
      classes.push('tool-error');
    } else {
      classes.push('tool-complete');
    }
    
    return classes.join(' ');
  }
}
