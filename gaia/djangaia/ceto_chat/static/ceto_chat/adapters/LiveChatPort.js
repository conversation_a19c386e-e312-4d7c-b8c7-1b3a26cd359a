// LiveChatPort.js
// ChatPort adapter for live MCP server connections
// Wraps existing chat_mcp_utils.js functionality in the port interface

import { checkMcpServer, loadMcpTools, callTool, sendChat } from '../chat_mcp_utils.js';

export class LiveChatPort {
  constructor(mcpApiBase = 'http://localhost:9000') {
    this.name = 'LiveChatPort';
    this.mcpApiBase = mcpApiBase;
  }
  
  async loadTools(ctx) {
    return await loadMcpTools(ctx);
  }
  
  async callTool(ctx, toolName, toolArgs) {
    return await callTool(ctx, toolName, toolArgs);
  }
  
  async sendChat(ctx, messageText) {
    return await sendChat(ctx, messageText);
  }
  
  async isAvailable(ctx) {
    try {
      const result = await checkMcpServer(ctx, this.mcpApiBase);
      return result?.ok || false;
    } catch (error) {
      return false;
    }
  }
  
  // Additional utility methods
  getApiBase() {
    return this.mcpApiBase;
  }
  
  setApiBase(newBase) {
    this.mcpApiBase = newBase;
  }
}
