// LiveChatPort.js
// ChatPort adapter for live MCP server connections
// Wraps existing chat_mcp_utils.js functionality in the port interface

import { checkMcpServer, loadMcpTools, callTool, sendChat } from '../chat_mcp_utils.js';

export class LiveChatPort {
  constructor(mcpApiBase = 'http://localhost:9000') {
    this.name = 'LiveChatPort';
    this.mcpApiBase = mcpApiBase;
  }
  
  async loadTools(ctx) {
    return await loadMcpTools(ctx);
  }
  
  async callTool(ctx, toolName, toolArgs) {
    return await callTool(ctx, toolName, toolArgs);
  }
  
  async sendChat(ctx, messageText) {
    return await sendChat(ctx, messageText);
  }
  
  async isAvailable(ctx = null) {
    console.log('🔍 LiveChatPort.isAvailable checking:', this.mcpApiBase);

    try {
      // Simple direct check without using checkMcpServer to avoid context issues
      const url = `${this.mcpApiBase}/ceto_chat/api/mcp/tools/`;
      console.log('📡 Direct fetch to:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        console.warn('❌ Response not OK:', response.status, response.statusText);
        return false;
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.warn('❌ Response is not JSON:', contentType);
        return false;
      }

      const data = await response.json();
      console.log('📥 Response data:', data);

      const isAvailable = data && data.success === true;
      console.log('✅ LiveChatPort.isAvailable result:', isAvailable);

      return isAvailable;

    } catch (error) {
      console.warn('❌ LiveChatPort.isAvailable error:', error);
      return false;
    }
  }
  
  // Additional utility methods
  getApiBase() {
    return this.mcpApiBase;
  }
  
  setApiBase(newBase) {
    this.mcpApiBase = newBase;
  }
}
