// MockChatPort.js
// ChatPort adapter for mock/offline functionality
// Wraps existing mock_tools.js and mock_chat.js in the port interface

import { CetoMockTools } from '../mock_tools.js';
import { mockChat } from '../mock_chat.js';

export class MockChatPort {
  constructor() {
    this.name = 'MockChatPort';
  }
  
  async loadTools(ctx) {
    const tools = Object.keys(CetoMockTools).map(name => ({
      name,
      description: CetoMockTools[name].description || `Mock ${name} tool`,
      parameters: CetoMockTools[name].parameters || {}
    }));
    
    return { 
      ok: true, 
      tools, 
      connected: false, 
      mockUsed: true 
    };
  }
  
  async callTool(ctx, toolName, toolArgs) {
    const tool = CetoMockTools[toolName];
    if (!tool) {
      return { 
        jsonrpc: '2.0', 
        error: { 
          code: -1, 
          message: `Tool ${toolName} not found in mock tools` 
        } 
      };
    }
    
    try {
      return await tool.invoke(ctx, toolArgs);
    } catch (error) {
      return {
        jsonrpc: '2.0',
        error: {
          code: -2,
          message: `Mock tool error: ${error.message}`
        }
      };
    }
  }
  
  async sendChat(ctx, messageText) {
    try {
      const res = await mockChat(messageText, ctx);
      
      if (res && res.success) {
        return {
          ok: true,
          connected: false,
          mockUsed: true,
          assistantMessage: res.message,
          transcriptDelta: [
            { role: 'user', content: messageText },
            { role: 'assistant', content: res.message, mock_mode: true }
          ]
        };
      }
      
      return { 
        ok: false, 
        connected: false, 
        mockUsed: true, 
        errorMessage: 'Mock chat failed' 
      };
      
    } catch (error) {
      return {
        ok: false,
        connected: false,
        mockUsed: true,
        errorMessage: `Mock chat error: ${error.message}`
      };
    }
  }
  
  async isAvailable() {
    // Mock is always available
    return true;
  }
  
  // Get list of available mock tools
  getAvailableTools() {
    return Object.keys(CetoMockTools);
  }
}
