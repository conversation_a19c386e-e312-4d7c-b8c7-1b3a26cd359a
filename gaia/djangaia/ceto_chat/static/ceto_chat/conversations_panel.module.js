// ConversationsPanel – simple UI to manage conversations
// Follows the same pattern as DebugPanelComponent and relies on $parent state/methods
// Expects ConversationsMixin on the parent app instance

export default {
  name: 'ConversationsPanel',
  props: {
    showHeader: { type: Boolean, default: true }
  },
  methods: {
    create() { this.$parent && this.$parent.createNewConversation && this.$parent.createNewConversation(); },
    refresh() { this.$parent && this.$parent.loadConversations && this.$parent.loadConversations(); },
    select(conv) { this.$parent && this.$parent.selectConversation && this.$parent.selectConversation(conv); },
    remove(conv) { this.$parent && this.$parent.deleteConversation && this.$parent.deleteConversation(conv); },
    fmt(ts) { if (!ts) return ''; try { const d = new Date(ts); return d.toLocaleString(); } catch(_) { return ts; } },
    title(conv) { return conv?.title || (conv?.first_user_message && conv.first_user_message.split(/\s+/).slice(0,4).join(' ')) || conv?.id || 'Untitled'; }
  },
  template: `
    <div class="conversations-panel" style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 6px; background:#fafafa;">
      <div v-if="showHeader" style="display:flex; align-items:center; justify-content:space-between; gap:8px; margin-bottom:6px;">
        <div style="font-weight:600; color:#333;">Conversations</div>
        <div style="display:flex; gap:6px;">
          <button class="btn btn-sm btn-outline-secondary" @click="refresh" title="Refresh list"><i class="fas fa-sync-alt"></i></button>
          <button class="btn btn-sm btn-primary" @click="create" :disabled="$parent.creatingConversation"><i class="fas fa-plus"></i> New</button>
        </div>
      </div>

      <div v-if="$parent.conversationLoadError" style="color:#721c24; background:#f8d7da; border:1px solid #f5c6cb; padding:6px; border-radius:4px; font-size:13px;">Failed to load conversations. Try refresh.</div>

      <div v-if="!$parent.conversations || $parent.conversations.length === 0" style="font-size:13px; color:#666;">No conversations yet. Create your first.</div>

      <div v-else class="conv-list" style="display:flex; flex-direction:column; gap:6px;">
        <div v-for="c in $parent.conversations" :key="c.id" class="conv-item" @click="select(c)" style="display:flex; align-items:center; justify-content:space-between; padding:6px; border:1px solid #eee; border-radius:4px; background:#fff; cursor: pointer;">
          <div style="min-width:0;">
            <div style="font-weight:600; color:#333; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; max-width:420px;" :title="title(c)">{{ title(c) }}</div>
            <div style="font-size:12px; color:#888;">Updated: {{ fmt(c.updated_at) }}</div>
          </div>
          <div style="display:flex; gap:6px;">
            <button class="btn btn-sm btn-outline-danger" @click.stop="remove(c)"><i class="fas fa-trash"></i></button>
          </div>
        </div>
      </div>
    </div>
  `
};

