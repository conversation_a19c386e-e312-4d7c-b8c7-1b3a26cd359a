// API helper utilities for Ceto Chat
// Safe JSON fetch: returns { response, data }
export async function fetchJsonOrError(url, options = {}) {
  const response = await fetch(url, options);
  const contentType = (response.headers && response.headers.get && response.headers.get('content-type')) || '';
  let data;
  if (contentType && contentType.toLowerCase().includes('application/json')) {
    try { data = await response.json(); }
    catch (e) { data = { success: false, error: `Invalid JSON from server: ${e.message}`, status: response.status }; }
  } else {
    const text = await response.text();
    data = { success: false, error: 'Non-JSON response from server', status: response.status, content_type: contentType || 'unknown', text: (text || '').slice(0, 2000) };
  }
  return { response, data };
}

export default { fetchJsonOrError };

