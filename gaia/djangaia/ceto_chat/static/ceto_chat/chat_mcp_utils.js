// Chat MCP utilities (ESM)
// Exports helper functions and uses ESM imports for mocks

import { CetoMockTools } from './mock_tools.js';
import { mockChat } from './mock_chat.js';

const DEFAULT_MCP_API_BASE = (typeof window !== 'undefined' && typeof window.MCP_API_BASE !== 'undefined') ? window.MCP_API_BASE : 'http://localhost:9000';

export async function checkMcpServer(ctx, mcpApiBase) {
  const base = mcpApiBase || DEFAULT_MCP_API_BASE || 'http://127.0.0.1:9000';
  const url = `${base}/ceto_chat/api/mcp/tools/`;
  let callDetail = null;
  try {
    ctx?.addDebugLog && ctx.addDebugLog('MCP', 'SENT', 'Checking MCP server availability...');
    callDetail = ctx?.addCallDetail ? ctx.addCallDetail('GET', url) : null;

    // Use ctx.fetchJsonOrError if available, otherwise use direct fetch
    let response, data;
    if (ctx && ctx.fetchJsonOrError) {
      const result = await ctx.fetchJsonOrError(url);
      response = result.response;
      data = result.data;
    } else {
      // Fallback to direct fetch if context doesn't have fetchJsonOrError
      response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      data = await response.json().catch(() => ({}));
    }

    const ok = response.ok && data && data.success;
    if (ok) {
      callDetail && ctx?.updateCallDetail && ctx.updateCallDetail(callDetail, response.status, data);
      ctx?.addDebugLog && ctx.addDebugLog('MCP', 'RECEIVED', `MCP server available at ${base}`);
      return { ok: true, response, data };
    } else {
      callDetail && ctx?.updateCallDetail && ctx.updateCallDetail(callDetail, response.status, data, data?.error);
      ctx?.addDebugLog && ctx.addDebugLog('MCP', 'INFO', 'MCP server unavailable');
      return { ok: false, response, data, errorMessage: data?.error };
    }
  } catch (error) {
    callDetail && ctx?.updateCallDetail && ctx.updateCallDetail(callDetail, 'ERROR', null, error.message);
    ctx?.addDebugLog && ctx.addDebugLog('MCP', 'INFO', `MCP server unavailable: ${error.message}`);
    return { ok: false, response: null, data: null, errorMessage: error.message };
  }
}

export async function sendChat(ctx, messageText) {
  if (!messageText || !messageText.trim()) return { ok: false, reason: 'empty' };
  const raw = (typeof window !== 'undefined' && typeof window.MCP_API_BASE !== 'undefined' && window.MCP_API_BASE) ? window.MCP_API_BASE : DEFAULT_MCP_API_BASE || 'http://127.0.0.1:9000';
  const mcpBase = (raw && raw.includes('0.0.0.0')) ? 'http://127.0.0.1:9000' : raw;
  const restBase = mcpBase.endsWith('/mcp') ? mcpBase.slice(0, -4) : mcpBase;
  const url = `${restBase}/chat/message`;
  const payload = { message: messageText };
  let call = null;
  try {
    ctx?.addDebugLog && ctx.addDebugLog('HTTP', 'SENT', payload, url);
    call = ctx?.addCallDetail ? ctx.addCallDetail('POST', url, payload) : null;

    // Use ctx.fetchJsonOrError if available, otherwise use direct fetch
    let response, data;
    if (ctx && ctx.fetchJsonOrError) {
      const result = await ctx.fetchJsonOrError(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
      response = result.response;
      data = result.data;
    } else {
      // Fallback to direct fetch if context doesn't have fetchJsonOrError
      response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      data = await response.json().catch(() => ({}));
    }

    call && ctx?.updateCallDetail && ctx.updateCallDetail(call, response.status, data);
    ctx?.addDebugLog && ctx.addDebugLog('HTTP', 'RECEIVED', data, url);
    if (response.ok && data && data.success) {
      return { ok: true, connected: true, mockUsed: false, assistantMessage: data.message, transcriptDelta: [ { role: 'user', content: messageText }, { role: 'assistant', content: data.message } ], response, data };
    }
    throw new Error((data && (data.error || data.message)) || 'MCP chat failed');
  } catch (e) {
    ctx?.addErrorLog && ctx.addErrorLog(`MCP server error: ${e.message}. Using mock fallback.`, 'sendChat');
    const mockResponse = await mockChat(messageText, ctx);
    if (mockResponse && mockResponse.success) {
      return { ok: true, connected: false, mockUsed: true, assistantMessage: mockResponse.message, transcriptDelta: [ { role: 'user', content: messageText }, { role: 'assistant', content: mockResponse.message, mock_mode: true } ] };
    }
    return { ok: false, connected: false, mockUsed: false, errorMessage: e.message };
  }
}

export async function loadMcpTools(ctx) {
  const base = (typeof window.MCP_API_BASE !== 'undefined') ? window.MCP_API_BASE : 'http://localhost:9000';
  const url = `${base}/ceto_chat/api/mcp/tools/`;
  const callDetail = ctx?.addCallDetail ? ctx.addCallDetail('GET', url) : null;
  try {
    // Use ctx.fetchJsonOrError if available, otherwise use direct fetch
    let response, data;
    if (ctx && ctx.fetchJsonOrError) {
      const result = await ctx.fetchJsonOrError(url);
      response = result.response;
      data = result.data;
    } else {
      // Fallback to direct fetch if context doesn't have fetchJsonOrError
      response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      data = await response.json().catch(() => ({}));
    }

    const ok = response.ok && data && data.success;
    if (ok) {
      callDetail && ctx?.updateCallDetail && ctx.updateCallDetail(callDetail, response.status, data);
      ctx?.addDebugLog && ctx.addDebugLog('MCP', 'RECEIVED', `Loaded ${(data.tools || []).length} tools from ${data.server_url || ''}`);
      return { ok: true, tools: data.tools || [], serverUrl: data.server_url || '', response, data };
    } else {
      callDetail && ctx?.updateCallDetail && ctx.updateCallDetail(callDetail, response.status, data, data?.error);
      ctx?.addErrorLog && ctx.addErrorLog(data?.error || 'Failed to load MCP tools', 'loadMcpTools');
      return { ok: false, tools: [], serverUrl: '', response, data, errorMessage: data?.error };
    }
  } catch (error) {
    callDetail && ctx?.updateCallDetail && ctx.updateCallDetail(callDetail, 'ERROR', null, error.message);
    ctx?.addErrorLog && ctx.addErrorLog(`Network error loading MCP tools: ${error.message}`, 'loadMcpTools');
    return { ok: false, tools: [], serverUrl: '', response: null, data: null, errorMessage: error.message };
  }
}

export async function callTool(ctx, toolName, toolArgs = {}) {
  const requestData = { tool_name: toolName, tool_args: toolArgs };
  ctx.addDebugLog && ctx.addDebugLog('MCP_TOOL', 'SENT', `Calling ${toolName} with args: ${JSON.stringify(toolArgs)}`);
  const base = (typeof window.MCP_API_BASE !== 'undefined') ? window.MCP_API_BASE : 'http://localhost:9000';
  const url = `${base}/ceto_chat/api/mcp/call/`;
  const callDetail = ctx.addCallDetail ? ctx.addCallDetail('POST', url, requestData) : null;
  try {
    const response = await fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(requestData) });
    const data = await response.json().catch(() => ({}));
    if (response.ok && data && data.jsonrpc === '2.0') {
      if (data.result && Array.isArray(data.result.content)) {
        const textParts = data.result.content.filter(ci => ci && ci.type === 'text' && typeof ci.text === 'string').map(ci => ci.text);
        const combinedText = textParts.join('');
        const res = { success: true, text: combinedText, rpc: data, tool_name: data.tool_name || toolName, tool_args: data.tool_args || toolArgs };
        const idx = (combinedText || '').indexOf('<svg');
        if (idx !== -1) { res.format = 'svg'; res.text = combinedText.slice(idx); }
        callDetail && ctx.updateCallDetail && ctx.updateCallDetail(callDetail, response.status, data);
        ctx.addDebugLog && ctx.addDebugLog('MCP_TOOL', 'RECEIVED', JSON.stringify(data, null, 2));
        return { ok: true, result: res, response, data };
      } else if (data.error) {
        const errMsg = (data.error && (data.error.message || data.error)) || `Failed to call ${toolName} tool`;
        callDetail && ctx.updateCallDetail && ctx.updateCallDetail(callDetail, response.status, data, errMsg);
        ctx.addErrorLog && ctx.addErrorLog(errMsg, 'callTool');
        const mock = (CetoMockTools && CetoMockTools[toolName]);
        if (mock && typeof mock.invoke === 'function') {
          ctx.addDebugLog && ctx.addDebugLog('MCP_TOOL', 'INFO', `Falling back to mock tool after server error: ${toolName}`);
          const mockResult = await mock.invoke(ctx, toolArgs);
          return { ok: !!mockResult.success, result: mockResult, response, data };
        }
        return { ok: false, errorMessage: errMsg, response, data };
      }
      const errMsg = 'Unexpected JSON-RPC response shape';
      callDetail && ctx.updateCallDetail && ctx.updateCallDetail(callDetail, response.status, data, errMsg);
      ctx.addErrorLog && ctx.addErrorLog(errMsg, 'callTool');
      const mock = (CetoMockTools && CetoMockTools[toolName]);
      if (mock && typeof mock.invoke === 'function') {
        ctx.addDebugLog && ctx.addDebugLog('MCP_TOOL', 'INFO', `Falling back to mock tool after invalid response: ${toolName}`);
        const mockResult = await mock.invoke(ctx, toolArgs);
        return { ok: !!mockResult.success, result: mockResult, response, data };
      }
      return { ok: false, errorMessage: errMsg, response, data };
    }
    const errMsg = (data && (data.error || data.message)) || `Failed to call ${toolName} tool`;
    callDetail && ctx.updateCallDetail && ctx.updateCallDetail(callDetail, response.status, data, errMsg);
    ctx.addErrorLog && ctx.addErrorLog(errMsg, 'callTool');
    const mock = (CetoMockTools && CetoMockTools[toolName]);
    if (mock && typeof mock.invoke === 'function') {
      ctx.addDebugLog && ctx.addDebugLog('MCP_TOOL', 'INFO', `Falling back to mock tool after HTTP error: ${toolName}`);
      const mockResult = await mock.invoke(ctx, toolArgs);
      return { ok: !!mockResult.success, result: mockResult, response, data };
    }
    return { ok: false, errorMessage: errMsg, response, data };
  } catch (error) {
    callDetail && ctx.updateCallDetail && ctx.updateCallDetail(callDetail, 'ERROR', null, error.message);
    ctx.addErrorLog && ctx.addErrorLog(`Network error calling ${toolName} tool: ${error.message}`, 'callTool');
    try {
      const mock = (CetoMockTools && CetoMockTools[toolName]);
      if (mock && typeof mock.invoke === 'function') {
        ctx.addDebugLog && ctx.addDebugLog('MCP_TOOL', 'INFO', `Falling back to mock tool after network error: ${toolName}`);
        const mockResult = await mock.invoke(ctx, toolArgs);
        return { ok: !!mockResult.success, result: mockResult, response: null, data: null };
      }
    } catch (e2) {
      ctx.addErrorLog && ctx.addErrorLog(`Mock fallback error (${toolName}): ${e2.message}`, 'callTool');
    }
    return { ok: false, errorMessage: `Network error: ${error.message}` };
  }
}

