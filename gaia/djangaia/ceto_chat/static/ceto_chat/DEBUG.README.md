# Debugging the Ceto Chat Frontend (ESM + Vue 3)

This guide contains practical steps to debug the JavaScript modules under `gaia/djangaia/ceto_chat/static/ceto_chat/` when running via Django’s `runserver`.

The frontend is native-ESM (no bundler), so the browser devtools are your primary tools. Most issues are either:
- Module loading problems (import graph, wrong script tag types, 404s)
- Vue template/runtime issues
- HTTP/API problems (MCP server or Django endpoints)

---

## 1) Always start here – quick checklist

- Hard refresh (disable cache)
  - In Chrome: DevTools > Network > check “Disable cache” and press Ctrl/Cmd+Shift+R.
- Check Console for red errors first (syntax, import failures, Vue warnings).
- Check Network panel:
  - Scripts: 200 status for `ceto_app.module.js` and every `*.module.js`/`*.js` import.
  - API calls: 200/201 for `/ceto_chat/api/*` and MCP HTTP calls.
- Verify URL param: `?conversation_id=<id>` (for auto-loading a conversation).
- Toggle the Debug panel (bug button) and look at Call Details + Errors.

---

## 2) Module loading and ESM gotchas

Symptoms and fixes:

- Error: `Unexpected token 'export'`
  - Cause: An ESM file was loaded with a classic `<script src>` tag.
  - Fix: Only `ceto_app.module.js` is loaded via `<script type="module">`. All others must be imported.
  - Verify in template `ceto_chat_base.html` – there should be no classic script tags for ESM files.

- Error: `Failed to resolve module specifier` or `404 (Not Found)` for a module
  - Check the import path spelling and relative path (`./` vs `../`).
  - Look in the Network panel to see which URL the browser requested.
  - Static path is `/static/ceto_chat/...` when served by Django.

- Cycles or race-like behavior
  - Avoid mutual imports between components. If two parts need each other, extract a shared utility and import that instead.
  - Prefer `await import("./x.js")` at the leaf call site to break cycles.

---

## 3) Vue template/runtime issues

Common warnings/errors and how to resolve:

- `[Vue warn]: Template compilation error: v-else/v-else-if has no adjacent v-if`
  - Means the `v-else` block isn’t directly after a `v-if` block.
  - Fix by co-locating the blocks or removing the stray `v-else` section.

- Blank transcript after selecting a conversation
  - Ensure `selectedToolName` is set to `'raw_chat'` when loading a conversation. In our code, `conversations.js` sets this.
  - Check that `messages` is an array of `{ role, content }` objects.

- Component not appearing
  - Confirm it’s registered: `app.component('my-widget', MyWidget);`
  - Check the template tag name `<my-widget>` and casing (kebab-case in templates, PascalCase in JS).

- Use Vue Devtools
  - Install Vue Devtools (Vue 3) browser extension.
  - Inspect component tree, props, and reactive state (`messages`, `selectedToolName`, `mcpTools`, etc.).

---

## 4) HTTP/API debugging

Ceto Chat talks to two backends:
- Django endpoints under `/ceto_chat/api/*` (conversations)
- MCP HTTP server (tools/chat) at `window.MCP_API_BASE` (default `http://localhost:9000`)

Steps:

- Verify MCP base
  - In Console: `window.MCP_API_BASE` – ensure it’s reachable.
  - In Network panel, look for calls to `${MCP_API_BASE}/ceto_chat/api/mcp/*` or `${MCP_API_BASE}/chat/message`.

- Conversations API
  - List: `GET /ceto_chat/api/conversations/`
  - Create: `POST /ceto_chat/api/conversations/create/`
  - Load: `GET /ceto_chat/api/conversations/<id>/`
  - Append: `POST /ceto_chat/api/conversations/<id>/append/`
  - Delete: `DELETE /ceto_chat/api/conversations/<id>/delete/`

- Quick curl tests
  ```bash
  curl -i http://localhost:8000/ceto_chat/api/conversations/
  curl -i -X POST http://localhost:8000/ceto_chat/api/conversations/create/
  curl -i http://localhost:8000/ceto_chat/api/conversations/<id>/
  curl -i -X POST -H 'Content-Type: application/json' \
       -d '{"messages":[{"role":"user","content":"hello"}]}' \
       http://localhost:8000/ceto_chat/api/conversations/<id>/append/
  ```

- CORS / mixed-origin issues
  - MCP is often `http://localhost:9000` while Django runs at `http://localhost:8000`. The MCP server must allow the browser origin (CORS headers). If you see CORS errors, fix server headers or proxy through the same origin.

---

## 5) Built-in debugging hooks (Debug panel)

`debug.js` provides a mixin and component to record events:
- `addDebugLog(type, direction, data, url?)` – quick log entries
- `addCallDetail(method, url, requestBody?)` – begin request tracking
- `updateCallDetail(callEntry, status, responseBody?, error?)` – finalize request
- `addErrorLog(message, source?)` – capture errors

Where to use them:
- In utilities (e.g., `chat_mcp_utils.js`) around fetch calls
- In provider flows right before/after network calls

How to see logs:
- Click the bug button (top-right) to open the Debug panel
- Inspect the tabs: Call Details, API, History, Tools, Errors

---

## 6) Effective DevTools usage

- Console
  - `console.log` and `console.error` are fine, but prefer `addDebugLog` so logs show up in the Debug panel too.
  - Use `console.assert(condition, 'message')` to flag unexpected states.

- Sources
  - Set breakpoints inside `*.module.js` files.
  - Enable “Pause on exceptions” (including caught exceptions) to catch ESM import failures or runtime errors.

- Network
  - Verify each module and API call loads with 200 status.
  - Check response bodies; for non-JSON responses, our helpers surface `content_type` in error objects.

- Performance
  - If the transcript becomes large, monitor layout thrashing. The sticky input + scroll-to-bottom helper should be O(1) per message.

---

## 7) Forcing provider selection / testing mocks

Providers live in `chat_providers.js` and expose the same interface (`loadTools`, `callTool`, `sendChat`).

Ways to test MockProvider path:
- Stop MCP server (or set `window.MCP_API_BASE` to an unreachable address) and reload. The app will pick `MockProvider`.
- Temporarily stub the selector in the console:
  ```js
  import('./chat_providers.js').then(m => { m.current = m.MockProvider; });
  ```

Validation cues:
- Debug panel shows MCP status and whether mock mode is active.
- Loaded tools list will be from `CetoMockTools`.

---

## 8) URL & routing checks

- `conversations.js` uses `?conversation_id=<id>` in the URL for permalinks.
- On startup, it reads that param and auto-loads the conversation.
- On select/create, it updates the URL via `history.replaceState` (no reload).

If a conversation loads but the transcript is blank:
- Ensure the API response at `/api/conversations/<id>/` returns `messages` as an array of `{ role, content }`.
- Ensure the UI has `selectedToolName = 'raw_chat'` (the mixin sets this when loading).

---

## 9) Django-specific tips

- Static files
  - If a module 404s, check the path under `/static/ceto_chat/...`.
  - Ensure `STATIC_URL` and app static dirs are configured; `collectstatic` if applicable.

- Auth
  - Conversation endpoints are `login_required`. If you get 302/403, log in at `/ceto_chat/login/`.

---

## 10) Patterns for quick instrumentation

- Add temporary logging in a module:
  ```js
  // At top
  console.debug('[my_module] loaded');
  // Around fetch
  ctx.addDebugLog && ctx.addDebugLog('HTTP', 'SENT', payload, url);
  ```

- Watch reactive changes
  ```js
  // inside createApp({...})
  watch: {
    selectedToolName(v) { console.debug('selectedToolName ->', v); },
    messages(v) { console.debug('messages.length ->', v.length); }
  }
  ```

- Scroll anchoring
  ```js
  this.$nextTick(() => { this.scrollTranscriptToBottom && this.scrollTranscriptToBottom(); });
  ```

---

## 11) Common issues and resolutions

- Tools don’t list
  - Check GET `${MCP_API_BASE}/ceto_chat/api/mcp/tools/` network call in `chat_mcp_utils.loadMcpTools`.
  - Verify CORS if MCP is cross-origin.

- Append didn’t save
  - Confirm POST `/ceto_chat/api/conversations/<id>/append/` returns `{ success: true }`.
  - Ensure `conversation_id` is present in the URL; otherwise the app creates one and updates the URL before appending.

- Vue warnings remain after edits
  - Make sure you hard refresh and disable cache. Vue compiles templates at runtime; stale HTML can keep old structure around.

---

By following this checklist and using the built-in Debug panel + browser DevTools, you should be able to quickly isolate ESM import problems, Vue template issues, and HTTP/API failures.




## 12) Recent execution model updates (what to look for when debugging)

- Slash tools now persist
  - After a successful tool call, the UI appends two messages to the active conversation via `/append/`:
    - `role: 'user'` with the literal `/<tool> <args>` command
    - `role: 'tool'` with `content` as a JSON string: `{ tool, rpc }`
  - On reload, the transcript reconstructs the tool panel from that stored JSON.
  - If you don’t see tool results after a reload, check the `append` POST in Network and verify the stored message’s `role` and `content` format.

- Render ordering fix
  - The renderer normalizes ISO timestamps with microseconds (e.g., `...123456Z`) to milliseconds before parsing.
  - If new messages appear above old ones, ensure the message objects have `timestamp` or `created_at`. Check the parsed ts in Sources by setting a breakpoint in `renderItems` in `ceto_app.module.js`.

- Minimal checklist for tool-related bugs
  1) Slash input recognized (leading `/`)?
  2) MCP tool call made (see MCP POST in Network, or Debug panel Call Details)?
  3) Append POST fired with two messages (user and tool)?
  4) API `append` returned `{ success: true }`?
  5) Loader returns `role: 'tool'` entries on GET `/conversations/<id>/`?
  6) Renderer parses the JSON and displays a tool panel?

- Mock fallback behavior
  - If MCP fails, `callTool` can fall back to `CetoMockTools[tool].invoke` without breaking persistence; the same two-message append pattern is used.

- Tip: Quickly inspect stored tool payloads
  - Use the browser Console after loading a conversation:
    ```js
    $vm0.messages.filter(m => m.role === 'tool').map(m => JSON.parse(m.content))
    ```
  - Replace `$vm0` with the Vue app instance (inspect root in Vue Devtools to get a ref) if different.
