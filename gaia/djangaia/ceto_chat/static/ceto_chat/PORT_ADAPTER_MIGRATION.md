# Port/Adapter Pattern Migration

This document describes the migration from the legacy facade-based `useChat` to the new port/adapter pattern for progressive enhancement.

## Overview

The new architecture uses dependency injection with three types of ports:

- **ChatPort**: Handles communication (sending messages, calling tools)
- **RenderPort**: Handles presentation (formatting messages and tools for display)
- **EffectsPort**: Handles side effects (animations, scrolling, notifications)

## Progressive Enhancement Levels

### Level 1: Barebones (No Dependencies)
```javascript
import { createBarebones } from './adapters/index.js';
import { useChat } from './core/app/useChat.js';

const { chatPort, renderPort, effectsPort } = createBarebones();
const chat = useChat(chatPort, renderPort, effectsPort);

// Simple echo chat, works immediately
await chat.send('Hello!');
```

### Level 2: Mock Functionality (Offline)
```javascript
import { createMock } from './adapters/index.js';

const { chatPort, renderPort, effectsPort } = createMock();
const chat = useChat(chatPort, renderPort, effectsPort);

// Mock tools and responses, no network required
await chat.send('/web_search test query');
```

### Level 3: Live MCP (Network-Based)
```javascript
import { createLive } from './adapters/index.js';

const { chatPort, renderPort, effectsPort } = createLive('http://localhost:9000');
const chat = useChat(chatPort, renderPort, effectsPort);

// Real MCP tools and LLM responses
await chat.send('What is the weather like?');
```

### Level 4: Enhanced Rendering (Vue Components)
```javascript
import { LiveChatPort, VueRenderPort } from './adapters/index.js';

const chatPort = new LiveChatPort('http://localhost:9000');
const renderPort = new VueRenderPort();
const chat = useChat(chatPort, renderPort, null);

// Rich component rendering with syntax highlighting, JSON formatting
```

### Level 5: Full Effects (Animations, Scrolling)
```javascript
import { LiveChatPort, VueRenderPort, CetoEffectsPort } from './adapters/index.js';

const chatPort = new LiveChatPort('http://localhost:9000');
const renderPort = new VueRenderPort();
const effectsPort = new CetoEffectsPort({
  enableAnimations: true,
  enableScrolling: true
});

const chat = useChat(chatPort, renderPort, effectsPort);

// Full experience with loading spinners, auto-scroll, notifications
```

### Auto-Detection
```javascript
import { createAuto } from './adapters/index.js';

const config = await createAuto({
  mcpApiBase: 'http://localhost:9000',
  effectsOptions: { enableAnimations: true }
});

const chat = useChat(config.chatPort, config.renderPort, config.effectsPort);
console.log(`Using level: ${config.level}`); // 'live' or 'mock'
```

## Migration Guide

### Before (Legacy)
```javascript
// Old facade pattern
export function useChat(ctx) {
  const state = new Proxy({}, {
    get(_t, prop) { return ctx[prop]; },
    set(_t, prop, value) { ctx[prop] = value; return true; }
  });
  
  const sendChat = ctx.sendChat ? ctx.sendChat.bind(ctx) : async () => ({ ok: false });
  
  return { state, sendChat, /* ... */ };
}

// Usage
const chat = useChat(this); // 'this' is Vue component context
```

### After (Port/Adapter)
```javascript
// New port-based pattern
export function useChat(chatPort, renderPort, effectsPort) {
  const messages = ref([]);
  const timeline = ref([]);
  
  async function send(text) {
    // Core logic here, not delegated
    if (text.startsWith('/')) {
      await handleToolCommand(text);
    } else {
      await handleChatMessage(text);
    }
  }
  
  return { messages, timeline, send, /* ... */ };
}

// Usage
const { chatPort, renderPort, effectsPort } = await createAuto();
const chat = useChat(chatPort, renderPort, effectsPort);
```

## Available Adapters

### Chat Ports
- `SimpleChatPort`: Echo responses, no tools
- `MockChatPort`: Mock tools and responses (offline)
- `LiveChatPort`: Real MCP server connection

### Render Ports
- `SimpleRenderPort`: Basic text formatting
- `VueRenderPort`: Rich Vue component rendering

### Effects Ports
- `CetoEffectsPort`: Animations, scrolling, notifications

## Factory Functions

The `PortFactory` provides convenient setup methods:

```javascript
import { portFactory } from './adapters/PortFactory.js';

// Quick setups
const barebones = portFactory.createBarebones();
const mock = portFactory.createMock();
const live = portFactory.createLive('http://localhost:9000');
const enhanced = portFactory.createEnhancedRender('live');
const full = portFactory.createFullEffects('live', null, { enableAnimations: true });

// Auto-detection
const auto = await portFactory.createAuto();

// Custom
const custom = portFactory.createCustom(
  new LiveChatPort(),
  new SimpleRenderPort(),
  new CetoEffectsPort()
);
```

## ChatWindow Component Changes

The `ChatWindow.module.js` component has been updated to:

1. Use Vue 3 Composition API with `setup()`
2. Initialize ports automatically with `createAuto()`
3. Delegate to chat kernel for core functionality
4. Maintain backward compatibility for existing methods

### Key Changes
- Added `setup()` function that initializes ports
- Updated computed properties to delegate to chat kernel
- Modified `handleSendUnified()` to use `chatKernel.send()`
- Enhanced `loadMcpTools()` and `callTool()` to use chat kernel
- Added conversation persistence helper

## Testing

The new pattern makes testing much easier:

```javascript
// Test with mock ports
const mockConfig = createMock();
const chat = useChat(mockConfig.chatPort, mockConfig.renderPort, null);

await chat.send('test message');
expect(chat.messages).toHaveLength(2); // user + assistant

await chat.send('/web_search test');
expect(chat.timeline).toContainEqual(
  expect.objectContaining({ kind: 'tool', toolName: 'web_search' })
);
```

## Benefits

1. **Progressive Enhancement**: Start simple, add features incrementally
2. **Clean Separation**: Chat logic, rendering, and effects are decoupled
3. **Easy Testing**: Inject mock ports for isolated testing
4. **Flexibility**: Mix and match different port implementations
5. **Backward Compatibility**: Existing components continue to work

## Examples

See `examples/progressive-enhancement.js` for complete working examples of all enhancement levels.
