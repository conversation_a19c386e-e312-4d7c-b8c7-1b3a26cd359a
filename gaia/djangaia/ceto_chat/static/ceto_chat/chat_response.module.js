// Unified ChatResponsePanel that can render both chat and tool responses (display-only)
// - If structured/JSON content is detected (object or JSON string), render with tool-style UI and JSON toggle
// - Otherwise, render plain chat text and the transcript (falling back to injected kernel when needed)

const ChatResponsePanel = {
  name: 'ChatResponsePanel',
  inject: ['chatKernel'],
  props: {
    // Unified optional response only
    response: { type: [String, Object], default: null },
    messages: { type: Array, default: () => [] },
    // Control whether to render transcript section (tool panels should hide it)
    showTranscript: { type: Boolean, default: true },
    // v-model for JSON toggle
    modelValue: { type: Boolean, default: undefined },
  },
  emits: ['update:modelValue'],
  data() {
    return { showFullJsonRpc: this.modelValue ?? false };
  },
  watch: {
    modelValue(v) { if (typeof v !== 'undefined' && v !== this.showFullJsonRpc) this.showFullJsonRpc = v; },
    showFullJsonRpc(v) { this.$emit('update:modelValue', v); }
  },
  computed: {
    chat() { return this.chatKernel ? this.chatKernel() : null; },
    normalizedResponse() {
      const fallbackKernelResp = (this.chat && this.chat.state) ? this.chat.state.lastAssistantResponse : '';
      const source = (this.response != null) ? this.response : fallbackKernelResp;
      return this.normalizeContent(source);
    },
    isStructuredContent() {
      const r = this.normalizedResponse;
      return !!(r && (r.isObject || r.hasJsonContent || r.rpc));
    },
    panelStyle() {
      const ok = (this.normalizedResponse && this.normalizedResponse.success) !== false;
      return {
        backgroundColor: ok ? '#d4edda' : '#f8d7da',
        border: ok ? '1px solid #c3e6cb' : '1px solid #f5c6cb',
        color: ok ? '#155724' : '#721c24',
        marginTop: '8px', padding: '10px', borderRadius: '3px'
      };
    }
  },
  methods: {
    tryParseJson(str) {
      if (typeof str !== 'string') return null;
      try { return JSON.parse(str); } catch (_) { return null; }
    },
    normalizeContent(content) {
      if (!content) return null;
      if (typeof content === 'string') {
        const parsed = this.tryParseJson(content);
        return {
          text: content,
          isObject: false,
          hasJsonContent: !!parsed,
          jsonData: parsed,
          success: true
        };
      }
      if (typeof content === 'object') {
        // Tool-like object { success, text, rpc, error, format }
        const text = content.text || content.content || (content.rpc ? JSON.stringify(content.rpc) : JSON.stringify(content));
        return {
          text,
          isObject: true,
          hasJsonContent: true,
          jsonData: content.rpc || content,
          success: content.success !== false,
          format: content.format,
          rpc: content.rpc,
          error: content.error
        };
      }
      return null;
    }
  },
  template: `
    <div style="margin-top: 12px;">
      <!-- Structured/JSON branch -->
      <div v-if="normalizedResponse && isStructuredContent" :style="panelStyle">
        <div style="display:flex; align-items:center; gap:8px;">
          <h4 style="margin:0;">{{ (normalizedResponse.success !== false) ? 'Response' : 'Error Response' }}</h4>
          <label style="font-size:12px; color:#666; display:flex; align-items:center; gap:4px;">
            <input type="checkbox" v-model="showFullJsonRpc"> Show full JSON
          </label>
        </div>

        <div v-if="normalizedResponse.format === 'svg'" style="background:#fff; padding:8px; border:1px solid #ddd; border-radius:3px; margin-top:6px;">
          <div v-html="normalizedResponse.text"></div>
        </div>
        <div v-else>
          <div v-if="normalizedResponse.success !== false">
            <strong>Content:</strong> <span v-text="normalizedResponse.text"></span>
            <br><small><strong>Format:</strong> <span>{{ normalizedResponse.rpc ? 'JSON-RPC 2.0' : 'JSON' }}</span></small>
          </div>
          <div v-else>
            <strong>Error:</strong> <span v-text="normalizedResponse.error || normalizedResponse.text"></span>
          </div>
          <br v-if="showFullJsonRpc">
          <small v-if="showFullJsonRpc && normalizedResponse.jsonData"><strong>Full JSON:</strong></small>
          <pre v-if="showFullJsonRpc && normalizedResponse.jsonData" class="response-box" style="background:#f8f9fa; margin-top:4px; white-space:pre-wrap; overflow:auto;">
{{ JSON.stringify(normalizedResponse.jsonData, null, 2) }}
          </pre>
        </div>
      </div>

      <!-- Plain chat branch -->
      <div v-else-if="normalizedResponse">
        <div class="response-box" style="margin-top:8px;">
          <div style="font-weight:600; margin-bottom:6px; color:#333;">Response</div>
          <div v-text="normalizedResponse.text"></div>
        </div>
      </div>

      <!-- Transcript -->
      <div v-if="showTranscript && ((messages && messages.length) || (chat && chat.state && chat.state.messages && chat.state.messages.length))" style="margin-top:16px;">
        <h4 style="margin:0 0 6px 0;">Transcript</h4>
        <div v-for="(m, idx) in (messages && messages.length ? messages : chat.state.messages)" :key="idx" class="message-item">
          <span class="role">{{ m.role }}:</span>
          <span v-text="m.content"></span>
        </div>
      </div>
    </div>
  `
};

export default ChatResponsePanel;
