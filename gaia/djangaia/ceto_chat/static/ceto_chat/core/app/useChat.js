// useChat.js
// Port/Adapter pattern implementation for progressive enhancement
// Accepts Chat<PERSON>ort, RenderPort, and optional EffectsPort for clean separation of concerns

import { ref, computed, readonly } from 'vue';

export function useChat(chatPort, renderPort, effectsPort) {
  // Core reactive state
  const messages = ref([]);
  const timeline = ref([]);
  const sending = ref(false);
  const nextEventId = ref(1);

  // Computed render items combining messages and timeline
  const renderItems = computed(() => {
    const messageItems = messages.value.map((m, i) => {
      const role = (m?.role || '').toLowerCase();
      return role === 'tool'
        ? renderPort.renderTool({ ...m, id: m.id || `msg-${i}` })
        : renderPort.renderChat({ ...m, id: m.id || `msg-${i}` });
    });

    const timelineTools = timeline.value
      .filter(e => e && e.kind === 'tool' && e.result)
      .map(e => renderPort.renderTool(e))
      .filter(Boolean);

    const allItems = [...messageItems, ...timelineTools];
    return renderPort.renderTimeline ? renderPort.renderTimeline(allItems) : allItems;
  });

  // Unified send function handling both chat and tool commands
  async function send(text) {
    if (!text?.trim()) return;

    sending.value = true;
    effectsPort?.onSending?.();

    try {
      if (text.startsWith('/')) {
        // Tool invocation
        await handleToolCommand(text);
      } else {
        // Regular chat
        await handleChatMessage(text);
      }

      effectsPort?.onDelivered?.(renderItems.value);

    } catch (error) {
      effectsPort?.onError?.(error);
      console.error('Chat error:', error);
    } finally {
      sending.value = false;
    }
  }

  // Handle tool command (slash command)
  async function handleToolCommand(text) {
    const rest = text.slice(1).trim();
    const spaceIndex = rest.indexOf(' ');
    const toolName = (spaceIndex === -1 ? rest : rest.slice(0, spaceIndex)).trim();
    const argsText = (spaceIndex === -1 ? '' : rest.slice(spaceIndex + 1)).trim();

    if (!toolName) return;

    const toolEvent = {
      id: nextEventId.value++,
      kind: 'tool',
      toolName,
      argsText,
      status: 'pending',
      result: null,
      ts: Date.now()
    };

    timeline.value.push(toolEvent);
    effectsPort?.onReceivePending?.();

    try {
      const result = await chatPort.callTool(null, toolName, { text: argsText });

      // Update the timeline event
      const eventIndex = timeline.value.findIndex(e => e.id === toolEvent.id);
      if (eventIndex >= 0) {
        timeline.value[eventIndex] = {
          ...toolEvent,
          result,
          status: 'complete'
        };
      }

      effectsPort?.onToolResult?.(result);

    } catch (error) {
      // Update timeline with error
      const eventIndex = timeline.value.findIndex(e => e.id === toolEvent.id);
      if (eventIndex >= 0) {
        timeline.value[eventIndex] = {
          ...toolEvent,
          result: { error: error.message },
          status: 'error'
        };
      }
      throw error;
    }
  }

  // Handle regular chat message
  async function handleChatMessage(text) {
    const userMessage = {
      role: 'user',
      content: text,
      ts: Date.now(),
      id: `user-${nextEventId.value++}`
    };

    messages.value.push(userMessage);

    // Add to timeline for unified view
    timeline.value.push({
      id: nextEventId.value++,
      kind: 'user',
      text,
      ts: Date.now()
    });

    effectsPort?.onReceivePending?.();

    const response = await chatPort.sendChat(null, text);

    if (response.ok) {
      const assistantMessage = {
        role: 'assistant',
        content: response.assistantMessage,
        mock_mode: response.mockUsed,
        ts: Date.now(),
        id: `assistant-${nextEventId.value++}`
      };

      messages.value.push(assistantMessage);

      // Add to timeline
      timeline.value.push({
        id: nextEventId.value++,
        kind: 'assistant',
        text: response.assistantMessage,
        ts: Date.now()
      });

      effectsPort?.onChatResponse?.(response);

      // Handle transcript delta if provided
      if (response.transcriptDelta && Array.isArray(response.transcriptDelta)) {
        // This could be used for persistence or other side effects
        effectsPort?.onTranscriptDelta?.(response.transcriptDelta);
      }

    } else {
      throw new Error(response.errorMessage || 'Chat failed');
    }
  }

  // Load tools from chat port
  async function loadTools() {
    try {
      effectsPort?.onLoadingTools?.();
      const result = await chatPort.loadTools(null);
      effectsPort?.onToolsLoaded?.(result);
      return result;
    } catch (error) {
      effectsPort?.onError?.(error);
      throw error;
    }
  }

  // Direct tool call (for programmatic use)
  async function callTool(toolName, toolArgs = {}) {
    try {
      const result = await chatPort.callTool(null, toolName, toolArgs);
      effectsPort?.onToolResult?.(result);
      return result;
    } catch (error) {
      effectsPort?.onError?.(error);
      throw error;
    }
  }

  return {
    // Reactive state (readonly to prevent external mutation)
    messages: readonly(messages),
    timeline: readonly(timeline),
    renderItems,
    sending: readonly(sending),

    // Core methods
    send,
    loadTools,
    callTool,

    // Render port integration
    resolve: renderPort.resolve?.bind(renderPort) || ((item) => item),

    // Legacy compatibility helpers
    sendChat: (text) => send(text),
    scrollTranscriptToBottom: () => effectsPort?.onScrollRequest?.()
  };
}

