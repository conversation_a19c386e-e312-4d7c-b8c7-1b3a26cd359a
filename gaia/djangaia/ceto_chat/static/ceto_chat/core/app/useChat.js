// useChat.js
// Minimal kernel facade over the existing root instance (no functionality change)
// - Exposes state and methods by delegating to the current Vue root component (ctx)
// - Keeps DOM CustomEvent emissions and HTTP logic inside existing methods
// - Safe to include without migrating components yet

export function useChat(ctx) {
  // Create a proxy so chat.state.X reads/writes ctx.X (single source of truth)
  const state = new Proxy({}, {
    get(_t, prop) { return ctx[prop]; },
    set(_t, prop, value) { ctx[prop] = value; return true; }
  });

  // Delegate methods to the existing instance; bind to preserve `this`
  const loadMcpTools = ctx.loadMcpTools ? ctx.loadMcpTools.bind(ctx) : async () => ({ ok: false });
  const sendChat = ctx.sendChat ? ctx.sendChat.bind(ctx) : async () => ({ ok: false });
  const callTool = ctx.callTool ? ctx.callTool.bind(ctx) : async () => ({ ok: false });
  const scrollTranscriptToBottom = ctx.scrollTranscriptToBottom ? ctx.scrollTranscriptToBottom.bind(ctx) : () => {};

  return {
    state,
    loadMcpTools,
    sendChat,
    callTool,
    scrollTranscriptToBottom,
  };
}

