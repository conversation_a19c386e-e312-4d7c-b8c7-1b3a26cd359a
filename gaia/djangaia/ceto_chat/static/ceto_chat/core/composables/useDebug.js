// useDebug.js – Composition replacement for DebugPanelMixin
// Usage (Options API safe): useDebug(this) to augment the component instance

import { renderMarkdown } from '../../utils/markdown.js';

export function useDebug(ctx) {
  // State defaults
  if (typeof ctx.debugPanelVisible === 'undefined') ctx.debugPanelVisible = false;
  if (typeof ctx.debugPaneMode === 'undefined') ctx.debugPaneMode = 'calls';
  if (typeof ctx.debugLogs === 'undefined') ctx.debugLogs = [];
  if (typeof ctx.callDetails === 'undefined') ctx.callDetails = [];
  if (typeof ctx.errorLog === 'undefined') ctx.errorLog = [];
  if (typeof ctx.debugLogCounter === 'undefined') ctx.debugLogCounter = 0;
  if (typeof ctx.callDetailCounter === 'undefined') ctx.callDetailCounter = 0;
  if (typeof ctx.errorCounter === 'undefined') ctx.errorCounter = 0;
  if (typeof ctx.devDocText === 'undefined') ctx.devDocText = '';
  if (typeof ctx.devDocHtml === 'undefined') ctx.devDocHtml = '';

  // Methods
  ctx.toggleDebugPanel = function toggleDebugPanel() { ctx.debugPanelVisible = !ctx.debugPanelVisible; console.log('Debug panel toggled:', ctx.debugPanelVisible); };
  ctx.setDebugPaneMode = function setDebugPaneMode(mode) { ctx.debugPaneMode = mode; console.log('Debug pane mode set to:', mode); };
  ctx.clearDebugLogs = function clearDebugLogs() { ctx.debugLogs = []; ctx.callDetails = []; ctx.errorLog = []; console.log('Debug logs cleared'); };
  ctx.resetAllState = function resetAllState() { ctx.clearDebugLogs(); ctx.debugPaneMode = 'calls'; console.log('Debug state reset'); };

  ctx.loadDoc = async function loadDoc(filename) {
    try {
      const resp = await fetch(`/static/ceto_chat/${filename}`);
      if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
      const text = await resp.text();
      ctx.devDocText = text;
      ctx.devDocHtml = await renderMarkdown(text);
    } catch (e) {
      const msg = `Failed to load ${filename}: ${e.message}`;
      ctx.devDocText = msg;
      ctx.devDocHtml = `<p class="error-text">${msg}</p>`;
    }
  };

  ctx.addDebugLog = function addDebugLog(type, direction, data, url = null) {
    const logEntry = { id: ++ctx.debugLogCounter, timestamp: new Date(), type, direction, data: typeof data === 'object' ? JSON.stringify(data, null, 2) : data, url };
    ctx.debugLogs.unshift(logEntry);
    if (ctx.debugLogs.length > 100) ctx.debugLogs = ctx.debugLogs.slice(0, 100);
    console.log('Debug log added:', logEntry);
  };

  ctx.addCallDetail = function addCallDetail(method, url, requestBody = null) {
    const callEntry = { id: ++ctx.callDetailCounter, timestamp: new Date(), method, url, status: 'PENDING', latency: null, sentBytes: requestBody ? new Blob([JSON.stringify(requestBody)]).size : 0, receivedBytes: 0, requestBody: requestBody ? JSON.stringify(requestBody, null, 2) : '', responseBody: '', error: null };
    callEntry.expanded = false; ctx.callDetails.unshift(callEntry);
    if (ctx.callDetails.length > 50) ctx.callDetails = ctx.callDetails.slice(0, 50);
    console.log('Call detail added:', callEntry);
    return callEntry;
  };

  ctx.updateCallDetail = function updateCallDetail(callEntry, status, responseBody = null, error = null) {
    callEntry.status = status; callEntry.completed = true; callEntry.latency = new Date() - callEntry.timestamp;
    if (responseBody) { callEntry.responseBody = typeof responseBody === 'object' ? JSON.stringify(responseBody, null, 2) : responseBody; callEntry.receivedBytes = new Blob([callEntry.responseBody]).size; }
    if (error) { callEntry.error = typeof error === 'object' ? JSON.stringify(error, null, 2) : error; }
    console.log('Call detail updated:', callEntry);
  };

  ctx.addErrorLog = function addErrorLog(message, source = null) {
    const errorEntry = { id: ++ctx.errorCounter, timestamp: new Date(), message: typeof message === 'object' ? JSON.stringify(message, null, 2) : message, source };
    ctx.errorLog.unshift(errorEntry);
    if (ctx.errorLog.length > 50) ctx.errorLog = ctx.errorLog.slice(0, 50);
    console.log('Error logged:', errorEntry);
  };

  ctx.formatTimestamp = function formatTimestamp(timestamp) { if (!timestamp) return ''; const date = new Date(timestamp); return date.toLocaleTimeString() + '.' + String(date.getMilliseconds()).padStart(3, '0'); };
  ctx.formatBytes = function formatBytes(bytes) { if (!bytes || bytes === 0) return '0 B'; const k = 1024; const sizes = ['B', 'KB', 'MB', 'GB']; const i = Math.floor(Math.log(bytes) / Math.log(k)); return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]; };
  ctx.toggleCallExpand = function toggleCallExpand(call) { call.expanded = !call.expanded; };

  // Optional: one-time init similar to mixin.mounted
  if (typeof ctx.__debug_inited === 'undefined') {
    ctx.__debug_inited = true;
    ctx.addDebugLog('SYSTEM', 'INFO', 'Debug panel initialized');
    const c = ctx.addCallDetail('GET', '/api/test');
    ctx.updateCallDetail(c, 200, { message: 'Test successful' });
    console.log('Debug composable initialized');
  }

  // Return minimal handles for external use/testing if desired
  return {
    get debugLogs() { return ctx.debugLogs; },
    addDebugLog: ctx.addDebugLog,
  };
}

