// useConversations.js – Composition replacement for ConversationsMixin
// Usage (Options API safe): in created() of a component, call useConversations(this)
// Expects the host to provide:
//  - computed/apiBase (string)
//  - method fetchJsonOrError(url, options) => { response, data }
//  - optional method addErrorLog(message, source)

export function useConversations(ctx) {
  // Ensure state defaults exist
  if (typeof ctx.conversations === 'undefined') ctx.conversations = [];
  if (typeof ctx.activeConversation === 'undefined') ctx.activeConversation = null;
  if (typeof ctx.creatingConversation === 'undefined') ctx.creatingConversation = false;
  if (typeof ctx.conversationLoadError === 'undefined') ctx.conversationLoadError = false;

  ctx.loadConversations = async function loadConversations() {
    try {
      ctx.addDebugLog && ctx.addDebugLog('API', 'OUT', 'GET /conversations/', `${ctx.apiBase}/conversations/`);
      const { response, data } = await ctx.fetchJsonOrError(`${ctx.apiBase}/conversations/`);
      if (response.ok && data.success) {
        ctx.conversationLoadError = false;
        ctx.conversations = (data.conversations || []).sort((a, b) => (b.updated_at || '').localeCompare(a.updated_at || ''));
        ctx.addDebugLog && ctx.addDebugLog('API', 'IN', data, `${ctx.apiBase}/conversations/`);
        // If URL has conversation_id param, auto-load that conversation
        try {
          const url = new URL(window.location.href);
          const convId = url.searchParams.get('conversation_id');
          if (convId) {
            await ctx.selectConversation({ id: convId });
          }
        } catch(_) {}
      } else {
        ctx.conversationLoadError = true;
        ctx.addErrorLog && ctx.addErrorLog(data.error || 'Failed to load conversations', 'loadConversations');
      }
    } catch (e) {
      ctx.conversationLoadError = true;
      ctx.addErrorLog && ctx.addErrorLog(`Network error: ${e.message}`, 'loadConversations');
    }
  };

  ctx.createNewConversation = async function createNewConversation() {
    if (ctx.creatingConversation) return;
    ctx.creatingConversation = true;
    try {
      const { response, data } = await ctx.fetchJsonOrError(`${ctx.apiBase}/conversations/create/`, {
        method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({})
      });
      if (response.status === 201 && data.success) {
        const conv = data.conversation;
        ctx.conversations.unshift(conv);
        ctx.activeConversation = conv;
        // Update URL with permalink to this conversation
        try {
          const url = new URL(window.location.href);
          url.searchParams.set('conversation_id', conv.id);
          window.history.replaceState({}, '', url);
        } catch (_) {}
      } else {
        ctx.addErrorLog && ctx.addErrorLog(data.error || 'Failed to create conversation', 'createNewConversation');
      }
    } catch (e) {
      ctx.addErrorLog && ctx.addErrorLog(`Network error: ${e.message}`, 'createNewConversation');
    } finally {
      ctx.creatingConversation = false;
    }
  };

  ctx.ensureConversationId = async function ensureConversationId() {
    try {
      const url = new URL(window.location.href);
      let conversationId = url.searchParams.get('conversation_id');
      if (!conversationId) {
        const { response, data } = await ctx.fetchJsonOrError(`${ctx.apiBase}/conversations/create/`, {
          method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({})
        });
        if (response.status === 201 && data.success) {
          conversationId = data.conversation.id;
          const newUrl = new URL(window.location.href);
          newUrl.searchParams.set('conversation_id', conversationId);
          window.history.replaceState({}, '', newUrl);
          // Update local state for immediate UX
          ctx.activeConversation = data.conversation;
          // Ensure newest conversation appears at top
          ctx.conversations = [data.conversation, ...(ctx.conversations || [])];
        } else {
          ctx.addErrorLog && ctx.addErrorLog(data.error || 'Failed to create conversation', 'ensureConversationId');
          return null;
        }
      }
      return conversationId;
    } catch (e) {
      ctx.addErrorLog && ctx.addErrorLog(`Network error: ${e.message}`, 'ensureConversationId');
      return null;
    }
  };

  ctx.appendTranscriptDelta = async function appendTranscriptDelta(delta) {
    try {
      const conversationId = await ctx.ensureConversationId();
      if (!conversationId) return false;
      const appendUrl = `${ctx.apiBase}/conversations/${encodeURIComponent(conversationId)}/append/`;
      const payload = { messages: (delta || []).map(d => ({ role: d.role, content: d.content })) };
      await fetch(appendUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
      // Refresh list so sidebar reflects newly created/updated conversation
      ctx.loadConversations && ctx.loadConversations();
      return true;
    } catch (e) {
      ctx.addErrorLog && ctx.addErrorLog(`Network error: ${e.message}`, 'appendTranscriptDelta');
      return false;
    }
  };

  ctx.selectConversation = async function selectConversation(conv) {
    try {
      const { response, data } = await ctx.fetchJsonOrError(`${ctx.apiBase}/conversations/${encodeURIComponent(conv.id)}/`);
      if (response.ok && data.success) {
        ctx.activeConversation = data.conversation;
        // Ensure raw_chat view is active so transcript displays
        ctx.selectedToolName = 'raw_chat';
        // Render messages into center pane (replacing current transcript)
        ctx.messages = (data.conversation.messages || []).map(m => ({
          role: m.role, content: m.content, timestamp: m.timestamp
        }));
        // Update permalink in URL without page reload
        try {
          const url = new URL(window.location.href);
          url.searchParams.set('conversation_id', data.conversation.id);
          window.history.replaceState({}, '', url);
        } catch (_) {}
        // Scroll to bottom to keep input visible near latest messages
        ctx.$nextTick && ctx.$nextTick(() => { if (ctx.scrollTranscriptToBottom) ctx.scrollTranscriptToBottom(); });
      } else {
        ctx.addErrorLog && ctx.addErrorLog(data.error || 'Failed to load conversation', 'selectConversation');
      }
    } catch (e) {
      ctx.addErrorLog && ctx.addErrorLog(`Network error: ${e.message}`, 'selectConversation');
    }
  };

  ctx.deleteConversation = async function deleteConversation(conv) {
    if (!confirm('Delete this conversation?')) return;
    try {
      const { response, data } = await ctx.fetchJsonOrError(`${ctx.apiBase}/conversations/${encodeURIComponent(conv.id)}/delete/`, { method: 'DELETE' });
      if (response.ok && data.success) {
        ctx.conversations = ctx.conversations.filter(c => c.id !== conv.id);
        if (ctx.activeConversation && ctx.activeConversation.id === conv.id) ctx.activeConversation = null;
      } else {
        ctx.addErrorLog && ctx.addErrorLog(data.error || 'Failed to delete conversation', 'deleteConversation');
      }
    } catch (e) {
      ctx.addErrorLog && ctx.addErrorLog(`Network error: ${e.message}`, 'deleteConversation');
    }
  };

  return {
    get conversations() { return ctx.conversations; },
    get activeConversation() { return ctx.activeConversation; },
  };
}

