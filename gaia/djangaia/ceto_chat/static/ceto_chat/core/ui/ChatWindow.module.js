// ChatWindow.module.js (root component with minimal external entrypoint)
// Uses container innerHTML as template. Registers needed components locally and owns state/logic.

import { ref, computed, onMounted } from 'vue';
import ChatResponsePanel from '../../chat_response.module.js';
import ConversationsPanel from '../../conversations_panel.module.js';
import { DebugPanelComponent } from '../../debug.js';
import { emitChatEvent } from '../../utils/events.js';
import { fetchJsonOrError } from '../../services/api.js';
import { useChat } from '../app/useChat.js';
import { useConversations } from '../composables/useConversations.js';
import { useDebug } from '../composables/useDebug.js';
import { portFactory } from '../../adapters/PortFactory.js';

export default {
  name: 'ChatWindow',
  components: {
    'chat-response-panel': Chat<PERSON>esponsePanel,
    'conversations-panel': ConversationsPanel,
    'debug-panel': DebugPanelComponent,
  },
  setup() {
    // Initialize ports and chat kernel
    const chatKernel = ref(null);
    const portsConfig = ref(null);

    // Initialize ports on mount
    onMounted(async () => {
      try {
        // Auto-detect best available configuration
        const config = await portFactory.createAuto({
          mcpApiBase: window.MCP_API_BASE || 'http://localhost:9000',
          effectsOptions: {
            eventEmitter: null, // Will be set up in created()
            scrollHandler: null, // Will be set up in created()
            debugLogger: null,   // Will be set up in created()
            errorLogger: null    // Will be set up in created()
          }
        });

        portsConfig.value = config;
        chatKernel.value = useChat(config.chatPort, config.renderPort, config.effectsPort);

        // Load tools after initialization
        await chatKernel.value.loadTools();

      } catch (error) {
        console.error('Failed to initialize chat ports:', error);
        // Fallback to barebones
        const fallbackConfig = portFactory.createBarebones();
        portsConfig.value = fallbackConfig;
        chatKernel.value = useChat(fallbackConfig.chatPort, fallbackConfig.renderPort, fallbackConfig.effectsPort);
      }
    });

    return {
      chatKernel,
      portsConfig
    };
  },
  provide() {
    return {
      chatKernel: () => this.chatKernel
    };
  },
  created() {
    // Initialize composables (augment this instance with conversation & debug capabilities)
    useConversations(this);
    useDebug(this);
  },
  data() {
    return {
      appVersion: '2.0.0', appName: 'Ceto Chat', chatProviderName: 'Direct',
      // MCP
      mcpTools: [], mcpServerUrl: '', mcpConnected: false, mcpLoading: false,
      selectedToolName: '', testMessage: 'Hello from frontend!', mcpToolLoading: false,
      lastToolResponse: null, toolResponses: [],
      // Legacy state for backward compatibility
      nextEventId: 1, showFullJsonRpc: false, mockModeActive: false,
      // Chat input
      userMessage: '', lastAssistantResponse: '',
      // Conversations (predeclared for reactivity)
      conversations: [], activeConversation: null, creatingConversation: false, conversationLoadError: false,
      // Debug panel state (predeclared for reactivity)
      debugPanelVisible: false, debugPaneMode: 'calls', debugLogs: [], callDetails: [], errorLog: [],
      debugLogCounter: 0, callDetailCounter: 0, errorCounter: 0, devDocText: '', devDocHtml: '',
    };
  },
  computed: {
    apiBase() { return window.location.origin + '/ceto_chat/api'; },
    mcpServerDisplay() {
      const candidate = (this.mcpServerUrl || '').trim();
      if (candidate) return candidate;
      const base = (typeof window !== 'undefined' && window.MCP_API_BASE) ? String(window.MCP_API_BASE) : '';
      return base || 'http://localhost:9000';
    },
    renderItems() {
      // Use chat kernel's renderItems if available, otherwise fallback to legacy logic
      if (this.chatKernel && this.chatKernel.renderItems) {
        return this.chatKernel.renderItems;
      }

      // Legacy fallback for backward compatibility
      const msgs = Array.isArray(this.messages) ? this.messages : [];
      const messageItems = msgs.map((m, i) => {
        const role = (m?.role || '').toLowerCase();
        return role === 'tool'
          ? this.toToolItemFromPersisted(m || {}, i)
          : this.toChatItem(m || {}, i);
      });

      const timelineTools = (Array.isArray(this.timeline) ? this.timeline : [])
        .filter(e => e && e.kind === 'tool')
        .map((e, j) => this.toToolItemFromTimeline(e || {}, j))
        .filter(Boolean);

      return [...messageItems, ...timelineTools].sort(this.compareRenderItems);
    },
    messages() {
      // Delegate to chat kernel if available
      return this.chatKernel?.messages || [];
    },
    timeline() {
      // Delegate to chat kernel if available
      return this.chatKernel?.timeline || [];
    },
    sending() {
      // Delegate to chat kernel if available
      return this.chatKernel?.sending || false;
    },
  },
  methods: {
    fetchJsonOrError,
    sendTool(toolName, argsString) {
      if (!toolName || typeof toolName !== 'string') return;
      return this.callTool(toolName, { text: argsString || '' });
    },
    async handleSendUnified() {
      const text = (this.userMessage || '').trim();
      if (!text) return;

      // Clear input immediately
      this.userMessage = '';

      // Use chat kernel if available
      if (this.chatKernel && this.chatKernel.send) {
        try {
          await this.chatKernel.send(text);

          // Handle conversation persistence
          await this.handleConversationPersistence(text);

        } catch (error) {
          console.error('Chat send error:', error);
          this.addErrorLog && this.addErrorLog(error.message, 'handleSendUnified');
        }
        return;
      }

      // Legacy fallback logic
      if (text.startsWith('/')) {
        const rest = text.slice(1).trim();
        const space = rest.indexOf(' ');
        const tool = (space === -1 ? rest : rest.slice(0, space)).trim();
        const args = (space === -1 ? '' : rest.slice(space + 1)).trim();
        this.selectedToolName = tool || '';

        const ev = {
          id: this.nextEventId++,
          kind: 'tool',
          toolName: this.selectedToolName,
          argsText: args,
          status: 'pending',
          result: null,
          ts: Date.now()
        };

        this.timeline.push(ev);
        const idx = this.timeline.length - 1;

        try {
          const res = await this.callTool(tool, { text: args });
          const resultObj = (res && res.ok && res.result) ? res.result : {
            success: false,
            error: res?.errorMessage || 'Tool call failed'
          };
          this.timeline[idx] = { ...this.timeline[idx], status: 'done', result: resultObj };

          await this.handleConversationPersistence(`/${tool}${args ? ' ' + args : ''}`);

        } catch (error) {
          this.timeline[idx] = {
            ...this.timeline[idx],
            status: 'done',
            result: { success: false, error: error.message || 'Tool call failed' }
          };
        }

        this.$nextTick && this.$nextTick(() => {
          if (this.scrollTranscriptToBottom) this.scrollTranscriptToBottom();
        });

      } else {
        this.selectedToolName = 'raw_chat';
        this.timeline.push({ id: this.nextEventId++, kind: 'user', text, ts: Date.now() });

        await this.sendChat();

        const lastAssistant = this.lastAssistantResponse;
        if (lastAssistant) {
          this.timeline.push({
            id: this.nextEventId++,
            kind: 'assistant',
            text: lastAssistant,
            ts: Date.now()
          });
        }

        this.$nextTick && this.$nextTick(() => {
          if (this.scrollTranscriptToBottom) this.scrollTranscriptToBottom();
        });
      }
    },

    // Helper method for conversation persistence
    async handleConversationPersistence(text) {
      try {
        const conversationId = this.activeConversation && this.activeConversation.id;
        if (conversationId) {
          const appendUrl = `${this.apiBase}/conversations/${encodeURIComponent(conversationId)}/append/`;

          let payload;
          if (text.startsWith('/')) {
            // Tool command
            const [tool, ...args] = text.slice(1).split(' ');
            payload = {
              messages: [
                { role: 'user', content: text },
                { role: 'tool', content: JSON.stringify({ tool, args: args.join(' ') }) }
              ]
            };
          } else {
            // Regular chat - let the chat kernel handle transcript delta
            if (this.chatKernel && this.chatKernel.messages) {
              const recentMessages = this.chatKernel.messages.slice(-2); // Get last 2 messages
              payload = {
                messages: recentMessages.map(m => ({
                  role: m.role,
                  content: m.content
                }))
              };
            } else {
              payload = { messages: [{ role: 'user', content: text }] };
            }
          }

          await fetch(appendUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
          });

          // Refresh conversations list to reflect newly created/updated conversation
          this.loadConversations && this.loadConversations();
        }
      } catch (error) {
        console.warn('Failed to persist conversation:', error);
      }
    },

    // ----- helper functions used by renderItems -----
    toTimestamp(t, idx) {
      if (!t) return idx;
      let s = String(t);
      s = s.replace(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\.(\d{3})\d+(.*)$/, '$1.$2$3');
      const n = Date.parse(s);
      return Number.isFinite(n) ? n : idx;
    },
    toChatItem(m = {}, i = 0) {
      const ts = this.toTimestamp(m.timestamp || m.created_at, i);
      return { id: `msg-${i}-${ts||0}`, kind: 'chat', role: m.role || 'user', text: m.content || '', ts };
    },
    toToolItemFromPersisted(m = {}, i = 0) {
      const ts = this.toTimestamp(m.timestamp || m.created_at, i);
      let parsed = null; try { parsed = (typeof m.content === 'string') ? JSON.parse(m.content) : (m.content || null); } catch(_) {}
      const toolName = (parsed && (parsed.tool || parsed.tool_name)) || '';
      const resultObj = parsed && (parsed.rpc || parsed);
      return { id: `toolmsg-${i}-${ts||0}`, kind: 'tool', status: 'done', result: resultObj || { success: false, error: 'Invalid tool payload' }, toolName, ts };
    },
    toToolItemFromTimeline(e = {}, j = 0) {
      const ts = (typeof e.ts === 'number') ? e.ts : (Date.now() + j);
      const isPending = (e.status || 'pending') === 'pending' && !e.result; const hasRealTool = !!(e.toolName && e.toolName !== 'raw_chat');
      if (isPending && !hasRealTool) return null;
      return { id: `tool-${(e.id!=null?e.id:j)}`, kind: 'tool', status: e.status || 'pending', result: e.result || null, toolName: e.toolName || '', ts };
    },
    compareRenderItems(a, b) {
      if (a.ts !== b.ts) return a.ts - b.ts;
      if (a.kind !== b.kind) return (a.kind === 'chat') ? -1 : 1;
      return String(a.id).localeCompare(String(b.id));
    },

    scrollTranscriptToBottom() {
      try {
        const el = document.getElementById('chat-transcript');
        if (el) el.scrollTop = el.scrollHeight;
      } catch (_) {}
    },

    async loadMcpTools() {
      this.mcpLoading = true;

      try {
        // Use chat kernel if available
        if (this.chatKernel && this.chatKernel.loadTools) {
          const result = await this.chatKernel.loadTools();

          this.mcpTools = result.tools || [];
          this.mcpConnected = result.connected || false;
          this.mockModeActive = result.mockUsed || false;
          this.chatProviderName = this.portsConfig?.level || 'Direct';

          if (this.portsConfig?.chatPort?.getApiBase) {
            this.mcpServerUrl = this.portsConfig.chatPort.getApiBase();
          }

          if (!result.ok && result.errorMessage) {
            this.addErrorLog(result.errorMessage, 'loadMcpTools');
          }

          return result;
        }

        // Legacy fallback
        const { initializeMcp } = await import('../../chat_providers.js');
        const r = await initializeMcp(this);
        this.chatProviderName = r.providerName || 'Direct';
        this.mcpTools = r.tools;
        this.mcpServerUrl = (r.serverUrl && String(r.serverUrl)) ||
                           (typeof window !== 'undefined' && window.MCP_API_BASE) || '';
        this.mcpConnected = r.connected;
        this.mockModeActive = r.mock;

        if (!r.ok && r.errorMessage) {
          this.addErrorLog(r.errorMessage, 'loadMcpTools');
        }

      } catch (e) {
        this.mcpConnected = false;
        this.mockModeActive = true;
        this.addErrorLog(`Network error loading MCP tools: ${e.message}`, 'loadMcpTools');
      } finally {
        this.mcpLoading = false;
      }
    },

    async callTool(toolName, toolArgs = {}) {
      this.mcpToolLoading = true;
      this.lastToolResponse = null;

      try {
        // Use chat kernel if available
        if (this.chatKernel && this.chatKernel.callTool) {
          const result = await this.chatKernel.callTool(toolName, toolArgs);

          if (result && result.jsonrpc === '2.0' && result.result) {
            this.lastToolResponse = result.result;
            this.toolResponses.push(result.result);

            this.$nextTick && this.$nextTick(() => {
              if (this.scrollTranscriptToBottom) this.scrollTranscriptToBottom();
            });

            return { ok: true, result: result.result };
          } else {
            const errorObj = {
              success: false,
              error: result?.error?.message || 'Tool call failed'
            };
            this.lastToolResponse = errorObj;
            this.toolResponses.push(errorObj);
            return { ok: false, errorMessage: errorObj.error };
          }
        }

        // Legacy fallback
        const { ensureProvider } = await import('../../chat_providers.js');
        const provider = await ensureProvider(this);
        const res = await provider.callTool(this, toolName, toolArgs);

        if (res && res.ok && res.result) {
          this.lastToolResponse = res.result;
          this.toolResponses.push(res.result);

          this.$nextTick && this.$nextTick(() => {
            if (this.scrollTranscriptToBottom) this.scrollTranscriptToBottom();
          });
        } else {
          const errorObj = { success: false, error: res?.errorMessage || 'Tool call failed' };
          this.lastToolResponse = errorObj;
          this.toolResponses.push(errorObj);
        }

        return res || { ok: false, errorMessage: 'No provider available' };

      } finally {
        this.mcpToolLoading = false;
      }
    },
    async sendChat() {
      const text = (this.userMessage || '').trim();
      if (!text) return;

      // Note: This method is now primarily for legacy compatibility
      // The main send logic is handled by handleSendUnified() -> chatKernel.send()

      if (this.chatKernel && this.chatKernel.send) {
        // Delegate to chat kernel
        return await this.chatKernel.send(text);
      }

      // Legacy fallback implementation
      this.userMessage = '';
      this.sending = true;
      emitChatEvent('send-start', { text });

      try {
        emitChatEvent('receive-pending', {});

        // Resolve provider once
        const cp = await import('../../chat_providers.js');
        const provider = (cp.current && cp.current.name === 'LiveProvider' && cp.current.sendChat)
          ? cp.current
          : (cp.current || cp.MockProvider);

        const res = await provider.sendChat(this, text);
        if (!res || !res.ok) {
          const errMsg = res?.errorMessage || 'Chat failed';
          emitChatEvent('error', { message: errMsg });
          this.addErrorLog && this.addErrorLog(errMsg, 'sendChat');
          return;
        }

        this.mcpConnected = !!res.connected;
        this.mockModeActive = !!res.mockUsed;

        // Stream transcript delta into local state and emit events
        let posted = false;
        let lastAssistantText = '';
        for (const d of (res.transcriptDelta || [])) {
          this.messages.push({
            role: d.role,
            content: d.content,
            timestamp: new Date().toISOString(),
            ...(d.mock_mode ? { mock_mode: true } : {})
          });

          if (!posted && d.role === 'user') {
            emitChatEvent('send-posted', { text: d.content });
            posted = true;
          }

          if (d.role === 'assistant') {
            lastAssistantText = d.content;
            emitChatEvent('receive-message', { text: d.content, delta: false });
          }
        }

        if (res.assistantMessage) this.lastAssistantResponse = res.assistantMessage;
        const finalText = res.assistantMessage || lastAssistantText || '';
        if (finalText) emitChatEvent('receive-end', { text: finalText });

        try {
          await this.appendTranscriptDelta(res.transcriptDelta || []);
        } catch (_) {}

        this.$nextTick && this.$nextTick(() => {
          if (this.scrollTranscriptToBottom) this.scrollTranscriptToBottom();
        });

      } catch (e) {
        const msg = (e && e.message) ? e.message : String(e);
        emitChatEvent('error', { message: msg });
        this.addErrorLog && this.addErrorLog(msg, 'sendChat');
      } finally {
        this.sending = false;
      }
    },
  },

  mounted() {
    this.addDebugLog && this.addDebugLog('SYSTEM', 'INFO', `${this.appName} v${this.appVersion} started`);

    // Load tools and conversations
    // Note: loadMcpTools will be called automatically by the setup() function when ports are initialized
    this.loadConversations();
  },
};

