// Debug Panel ESM exports (converted from previous globals)
import { renderMarkdown } from './utils/markdown.js';

export const DebugPanelMixin = {
    data() {
        return {
            debugPanelVisible: false,
            debugPaneMode: 'calls', // 'calls', 'api', 'history', 'tools', 'docs', 'errors'
            debugLogs: [],
            callDetails: [],
            errorLog: [],
            debugLogCounter: 0,
            callDetailCounter: 0,
            errorCounter: 0,
            // Developer docs inline preview state (moved from root)
            devDocText: '',
            devDocHtml: ''
        };
    },
    methods: {
        toggleDebugPanel() { this.debugPanelVisible = !this.debugPanelVisible; console.log('Debug panel toggled:', this.debugPanelVisible); },
        setDebugPaneMode(mode) { this.debugPaneMode = mode; console.log('Debug pane mode set to:', mode); },
        clearDebugLogs() { this.debugLogs = []; this.callDetails = []; this.errorLog = []; console.log('Debug logs cleared'); },
        resetAllState() { this.clearDebugLogs(); this.debugPaneMode = 'calls'; console.log('Debug state reset'); },
        async loadDoc(filename) {
            try {
                const resp = await fetch(`/static/ceto_chat/${filename}`);
                if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
                const text = await resp.text();
                this.devDocText = text;
                this.devDocHtml = await renderMarkdown(text);
            } catch (e) {
                const msg = `Failed to load ${filename}: ${e.message}`;
                this.devDocText = msg;
                this.devDocHtml = `<p class="error-text">${msg}</p>`;
            }
        },
        addDebugLog(type, direction, data, url = null) {
            const logEntry = { id: ++this.debugLogCounter, timestamp: new Date(), type, direction, data: typeof data === 'object' ? JSON.stringify(data, null, 2) : data, url };
            this.debugLogs.unshift(logEntry);
            if (this.debugLogs.length > 100) this.debugLogs = this.debugLogs.slice(0, 100);
            console.log('Debug log added:', logEntry);
        },
        addCallDetail(method, url, requestBody = null) {
            const callEntry = { id: ++this.callDetailCounter, timestamp: new Date(), method, url, status: 'PENDING', latency: null, sentBytes: requestBody ? new Blob([JSON.stringify(requestBody)]).size : 0, receivedBytes: 0, requestBody: requestBody ? JSON.stringify(requestBody, null, 2) : '', responseBody: '', error: null, completed: false };
            callEntry.expanded = false; this.callDetails.unshift(callEntry);
            if (this.callDetails.length > 50) this.callDetails = this.callDetails.slice(0, 50);
            console.log('Call detail added:', callEntry);
            return callEntry;
        },
        updateCallDetail(callEntry, status, responseBody = null, error = null) {
            callEntry.status = status; callEntry.completed = true; callEntry.latency = new Date() - callEntry.timestamp;
            if (responseBody) { callEntry.responseBody = typeof responseBody === 'object' ? JSON.stringify(responseBody, null, 2) : responseBody; callEntry.receivedBytes = new Blob([callEntry.responseBody]).size; }
            if (error) { callEntry.error = typeof error === 'object' ? JSON.stringify(error, null, 2) : error; }
            console.log('Call detail updated:', callEntry);
        },
        addErrorLog(message, source = null) {
            const errorEntry = { id: ++this.errorCounter, timestamp: new Date(), message: typeof message === 'object' ? JSON.stringify(message, null, 2) : message, source };
            this.errorLog.unshift(errorEntry);
            if (this.errorLog.length > 50) this.errorLog = this.errorLog.slice(0, 50);
            console.log('Error logged:', errorEntry);
        },
        formatTimestamp(timestamp) { if (!timestamp) return ''; const date = new Date(timestamp); return date.toLocaleTimeString() + '.' + String(date.getMilliseconds()).padStart(3, '0'); },
        formatBytes(bytes) { if (!bytes || bytes === 0) return '0 B'; const k = 1024; const sizes = ['B', 'KB', 'MB', 'GB']; const i = Math.floor(Math.log(bytes) / Math.log(k)); return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]; },
        toggleCallExpand(call) { call.expanded = !call.expanded; }
    },
    mounted() {
        this.addDebugLog('SYSTEM', 'INFO', 'Debug panel initialized');
        this.addCallDetail('GET', '/api/test');
        this.updateCallDetail(this.callDetails[0], 200, { message: 'Test successful' });
        console.log('Debug panel mixin mounted');
    }
};

export const DebugPanelComponent = {
    props: { showToolsTab: { type: Boolean, default: true }, visible: { type: Boolean, default: undefined } },
    methods: {
        handleToggle() { this.$emit('toggle'); this.$parent && this.$parent.toggleDebugPanel && this.$parent.toggleDebugPanel(); },
        handleReset() { this.$emit('reset'); this.$parent && this.$parent.resetAllState && this.$parent.resetAllState(); },
        handleClear() { this.$emit('clear'); this.$parent && this.$parent.clearDebugLogs && this.$parent.clearDebugLogs(); },
        setMode(mode) { this.$emit('set-pane-mode', mode); this.$parent && this.$parent.setDebugPaneMode && this.$parent.setDebugPaneMode(mode); },
        refreshTools() { this.$emit('refresh-tools'); this.$parent && this.$parent.loadMcpTools && this.$parent.loadMcpTools(); }
    },
    template: `
        <div v-if="visible !== undefined ? visible : $parent.debugPanelVisible" class="debug-panel">
            <div class="debug-header">
                <h5>Debug Console</h5>
                <div class="debug-controls">
                    <button @click="handleReset"
                            class="btn btn-sm btn-outline-warning"
                            title="Reset all debug state and re-initialize">
                        <i class="fas fa-redo"></i> RESET
                    </button>
                    <button @click="handleClear" class="btn btn-sm btn-outline-danger">Clear</button>
                    <button @click="handleToggle" class="btn btn-sm btn-outline-secondary">×</button>
                </div>
            </div>
            <div class="debug-tabs">
                <button @click="setMode('calls')" class="debug-tab" :class="{'active': $parent.debugPaneMode === 'calls'}">Call Details</button>
                <button @click="setMode('api')" class="debug-tab" :class="{'active': $parent.debugPaneMode === 'api'}">API Communications</button>
                <button @click="setMode('history')" class="debug-tab" :class="{'active': $parent.debugPaneMode === 'history'}">Conversation History</button>
                <button v-if="showToolsTab" @click="setMode('tools')" class="debug-tab" :class="{'active': $parent.debugPaneMode === 'tools'}">Tools Listing</button>
                <button @click="setMode('docs')" class="debug-tab" :class="{'active': $parent.debugPaneMode === 'docs'}">Developer Docs</button>
                <button @click="setMode('errors')" class="debug-tab" :class="{'active': $parent.debugPaneMode === 'errors'}">Errors <span v-if="$parent.errorLog.length > 0" class="badge badge-danger">{{ $parent.errorLog.length }}</span></button>
            </div>
            <div v-if="$parent.debugPaneMode === 'calls'" class="debug-content">
                <h6>HTTP Call Details</h6>
                <div v-if="$parent.callDetails.length === 0" class="debug-empty">No HTTP calls tracked yet. Send a message to see call details.</div>
                <div v-else>
                    <div class="call-details-table">
                        <div class="call-details-header">
                            <span>Time</span>
                            <span>Method</span>
                            <span>URL</span>
                            <span>Status</span>
                            <span>Latency</span>
                            <span>Sent</span>
                            <span>Received</span>
                        </div>
                        <div v-for="call in $parent.callDetails" :key="call.id" class="call-details-row" :class="{'call-error': call.status >= 400 || call.status === 'ERROR', 'call-pending': !call.completed}">
                            <span class="call-time" v-text="$parent.formatTimestamp(call.timestamp)"></span>
                            <span class="call-method" v-text="call.method"></span>
                            <span class="call-url"><a href="#" @click.prevent="$parent.toggleCallExpand(call)" :title="call.url" v-text="call.url"></a></span>
                            <span class="call-status" v-text="call.status"></span>
                            <span class="call-latency" v-text="call.latency ? call.latency + ' ms' : (call.completed ? '-' : 'pending')"></span>
                            <span class="call-sent" v-text="$parent.formatBytes(call.sentBytes)"></span>
                            <span class="call-received" v-text="$parent.formatBytes(call.receivedBytes)"></span>
                            <div v-if="call.expanded" class="call-row-details">
                                <div style="margin-bottom:6px"><strong>URL:</strong> {{ call.url }}</div>
                                <div v-if="call.requestBody" class="call-detail-section"><h6>Request</h6><pre class="debug-log-data" v-text="call.requestBody"></pre></div>
                                <div v-if="call.responseBody" class="call-detail-section"><h6>Response</h6><pre class="debug-log-data" v-text="call.responseBody"></pre></div>
                                <div v-if="call.error" class="call-detail-section"><h6>Error</h6><pre class="debug-log-data error-text" v-text="call.error"></pre></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="$parent.debugPaneMode === 'api'" class="debug-content">
                <h6>Raw API Data</h6>
                <div v-if="$parent.debugLogs.length === 0" class="debug-empty">No debug logs yet. Send a message to see server communications.</div>
                <div v-for="log in $parent.debugLogs" :key="log.id" class="debug-log-entry" :class="'debug-' + log.type.toLowerCase()">
                    <div class="debug-log-header">
                        <span class="debug-timestamp" v-text="$parent.formatTimestamp(log.timestamp)"></span>
                        <span class="debug-type" :class="'debug-type-' + log.type.toLowerCase()" v-text="log.type"></span>
                        <span class="debug-direction" :class="'debug-direction-' + log.direction.toLowerCase()" v-text="log.direction"></span>
                        <span v-if="log.url" class="debug-url" v-text="log.url"></span>
                    </div>
                    <pre class="debug-log-data" v-text="log.data"></pre>
                </div>
            </div>
            <div v-if="$parent.debugPaneMode === 'errors'" class="debug-content">
                <h6>Error Log</h6>
                <div v-if="$parent.errorLog.length === 0" class="debug-empty">No errors logged.</div>
                <div v-else>
                    <div v-for="err in $parent.errorLog" :key="err.id" class="debug-log-entry debug-error">
                        <div class="debug-log-header">
                            <span class="debug-timestamp" v-text="$parent.formatTimestamp(err.timestamp)"></span>
                            <span class="debug-type debug-type-error">ERROR</span>
                            <span v-if="err.source" class="debug-direction" v-text="err.source"></span>
                        </div>
                        <pre class="debug-log-data error-text" v-text="err.message"></pre>
                    </div>
                </div>
            </div>
            <div v-if="$parent.debugPaneMode === 'history'" class="debug-content">
                <h6>Conversation History</h6>
                <div v-if="!$parent.messages || $parent.messages.length === 0" class="debug-empty">No conversation history available yet.</div>
                <div v-else>
                    <div v-for="(m, idx) in $parent.messages" :key="idx" class="debug-log-entry">
                        <div class="debug-log-header">
                            <span class="debug-type" :class="{'debug-type-info': true}">{{ m.role }}</span>
                        </div>
                        <pre class="debug-log-data" v-text="m.content"></pre>
                    </div>
                </div>
            </div>
            <div v-if="$parent.debugPaneMode === 'tools'" class="debug-content">
                <h6>MCP Tools</h6>
                <span v-if="!$parent.mcpConnected && $parent.mockModeActive" style="margin-left: 8px; padding: 2px 6px; font-size: 10px; border-radius: 10px; background:#343a40; color:#ffc107; border:1px solid #6c757d;">Mock mode</span>
                <div class="mcp-status-bar" style="margin-bottom: 10px; padding: 8px; border-radius: 4px;" :style="{ backgroundColor: $parent.mcpConnected ? '#d4edda' : '#f8d7da', color: $parent.mcpConnected ? '#155724' : '#721c24', border: $parent.mcpConnected ? '1px solid #c3e6cb' : '1px solid #f5c6cb' }">
                    <i :class="$parent.mcpConnected ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'"></i>
                    <span v-if="$parent.mcpLoading">Loading MCP tools...</span>
                    <span v-else-if="$parent.mcpConnected">Connected to {{ $parent.mcpServerDisplay }} ({{ $parent.mcpTools.length }} tools)</span>
                    <span v-else>Not connected to MCP server</span>
                    <button @click="$parent.loadMcpTools" class="btn btn-sm btn-outline-secondary" style="float: right; margin-top: -2px;" :disabled="$parent.mcpLoading"><i class="fas fa-sync-alt" :class="{ 'fa-spin': $parent.mcpLoading }"></i> Refresh</button>
                </div>
                <div v-if="$parent.chatProviderName" style="margin-top: 6px; font-size: 12px; color: #555;"><strong>Provider:</strong> {{ $parent.chatProviderName }}</div>
                <div v-if="$parent.mcpTools.length === 0 && !$parent.mcpLoading" class="debug-empty">No MCP tools available. Check server connection.</div>
                <div v-else-if="$parent.mcpTools.length > 0" class="mcp-tools-list">
                    <div v-for="tool in $parent.mcpTools" :key="tool.name" class="mcp-tool-item" style="margin-bottom: 8px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;">
                        <div class="tool-header" style="font-weight: bold; color: #333;"><i class="fas fa-wrench" style="margin-right: 5px; color: #666;"></i>{{ tool.name }}</div>
                        <div class="tool-description" style="font-size: 0.9em; color: #666; margin-top: 4px;"><span v-text="tool.description || 'No description available'"></span></div>
                        <div v-if="tool.input_schema && Object.keys(tool.input_schema).length > 0" class="tool-schema" style="font-size: 0.8em; color: #888; margin-top: 4px;"><details><summary style="cursor: pointer;">Parameters</summary><pre style="margin-top: 4px; font-size: 0.75em; background: #fff; padding: 4px; border-radius: 2px;" v-text="JSON.stringify(tool.input_schema, null, 2)"></pre></details></div>
                    </div>
                </div>
                </div>

            <div v-if="$parent.debugPaneMode === 'docs'" class="debug-content">
                <h6>Developer Docs</h6>
                <div style="font-size:12px; color:#ccc; line-height:1.4;">
                    <p>Quick links:</p>
                    <ul style="margin-left: 16px;">
                        <li><a href="/static/ceto_chat/FE_README.md" target="_blank" style="color:#66ccff;">Architecture & Modules (FE_README.md)</a></li>
                        <li><a href="/static/ceto_chat/DEBUG.README.md" target="_blank" style="color:#66ccff;">Debugging Guide (DEBUG.README.md)</a></li>
                    </ul>
                    <p style="margin-top:8px;">Inline preview:</p>
                    <div style="display:flex; gap:8px; flex-wrap:wrap;">
                      <button class="btn btn-sm btn-outline-secondary" @click="$parent.loadDoc && $parent.loadDoc('FE_README.md')">Preview FE_README</button>
                      <button class="btn btn-sm btn-outline-secondary" @click="$parent.loadDoc && $parent.loadDoc('DEBUG.README.md')">Preview DEBUG</button>
                    </div>
                    <div style="margin-top:8px; background:#0f0f0f; padding:8px; border:1px solid #333; border-radius:4px; max-height:50vh; overflow:auto;">
                      <div v-if="$parent.devDocText" class="debug-log-data" v-html="$parent.devDocHtml || $parent.devDocText"></div>
                      <div v-else class="debug-empty">Select a document to preview or use the quick links above.</div>
                    </div>
                </div>
            </div>
        </div>
    `
};

export const DebugUtils = {
    logFunctionCall(functionName, args = []) { console.log(`[DEBUG] Function called: ${functionName}`, args); },
    logApiCall(method, url, data = null) { console.log(`[DEBUG] API Call: ${method} ${url}`, data); },
    logError(error, context = null) { console.error(`[DEBUG] Error${context ? ` in ${context}` : ''}:`, error); }
};

console.log('Debug panel ESM loaded');
