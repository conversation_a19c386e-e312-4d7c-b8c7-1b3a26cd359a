// Conversations module (ESM) – encapsulates conversation state and API calls
// This mixin expects the host component to provide:
// - computed apiBase (string)
// - method fetchJsonOrError(url, options) => { response, data }
// - optional method addErrorLog(message, source)

export const ConversationsMixin = {
  data() {
    return {
      conversations: [],
      activeConversation: null,
      creatingConversation: false,
      conversationLoadError: false
    };
  },
  methods: {
    async loadConversations() {
      try {
        const { response, data } = await this.fetchJsonOrError(`${this.apiBase}/conversations/`);
        if (response.ok && data.success) {
          this.conversationLoadError = false;
          this.conversations = (data.conversations || []).sort((a, b) => (b.updated_at || '').localeCompare(a.updated_at || ''));
          // If URL has conversation_id param, auto-load that conversation
          try {
            const url = new URL(window.location.href);
            const convId = url.searchParams.get('conversation_id');
            if (convId) {
              this.selectConversation({ id: convId });
            }
          } catch(_) {}
        } else {
          this.conversationLoadError = true;
          this.addErrorLog && this.addErrorLog(data.error || 'Failed to load conversations', 'loadConversations');
        }
      } catch (e) {
        this.conversationLoadError = true;
        this.addErrorLog && this.addErrorLog(`Network error: ${e.message}`, 'loadConversations');
      }
    },

    async createNewConversation() {
      if (this.creatingConversation) return;
      this.creatingConversation = true;
      try {
        const { response, data } = await this.fetchJsonOrError(`${this.apiBase}/conversations/create/`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({})
        });
        if (response.status === 201 && data.success) {
          const conv = data.conversation;
          this.conversations.unshift(conv);
          this.activeConversation = conv;
          // Update URL with permalink to this conversation
          try {
            const url = new URL(window.location.href);
            url.searchParams.set('conversation_id', conv.id);
            window.history.replaceState({}, '', url);
          } catch (_) {}
        } else {
          this.addErrorLog && this.addErrorLog(data.error || 'Failed to create conversation', 'createNewConversation');
        }
      } catch (e) {
        this.addErrorLog && this.addErrorLog(`Network error: ${e.message}`, 'createNewConversation');
      } finally {
        this.creatingConversation = false;
      }
    },

    async ensureConversationId() {
      try {
        const url = new URL(window.location.href);
        let conversationId = url.searchParams.get('conversation_id');
        if (!conversationId) {
          const { response, data } = await this.fetchJsonOrError(`${this.apiBase}/conversations/create/`, {
            method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({})
          });
          if (response.status === 201 && data.success) {
            conversationId = data.conversation.id;
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.set('conversation_id', conversationId);
            window.history.replaceState({}, '', newUrl);
            // Update local state for immediate UX
            this.activeConversation = data.conversation;
            // Ensure newest conversation appears at top
            this.conversations = [data.conversation, ...(this.conversations || [])];
          } else {
            this.addErrorLog && this.addErrorLog(data.error || 'Failed to create conversation', 'ensureConversationId');
            return null;
          }
        }
        return conversationId;
      } catch (e) {
        this.addErrorLog && this.addErrorLog(`Network error: ${e.message}`, 'ensureConversationId');
        return null;
      }
    },

    async appendTranscriptDelta(delta) {
      try {
        const conversationId = await this.ensureConversationId();
        if (!conversationId) return false;
        const appendUrl = `${this.apiBase}/conversations/${encodeURIComponent(conversationId)}/append/`;
        const payload = { messages: (delta || []).map(d => ({ role: d.role, content: d.content })) };
        await fetch(appendUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
        return true;
      } catch (e) {
        this.addErrorLog && this.addErrorLog(`Network error: ${e.message}`, 'appendTranscriptDelta');
        return false;
      }
    },


    async selectConversation(conv) {
      try {
        const { response, data } = await this.fetchJsonOrError(`${this.apiBase}/conversations/${encodeURIComponent(conv.id)}/`);
        if (response.ok && data.success) {
          this.activeConversation = data.conversation;
          // Ensure raw_chat view is active so transcript displays
          this.selectedToolName = 'raw_chat';
          // Render messages into center pane (replacing current transcript)
          this.messages = (data.conversation.messages || []).map(m => ({
            role: m.role, content: m.content, timestamp: m.timestamp
          }));
          // Update permalink in URL without page reload
          try {
            const url = new URL(window.location.href);
            url.searchParams.set('conversation_id', data.conversation.id);
            window.history.replaceState({}, '', url);
          } catch (_) {}
          // Scroll to bottom to keep input visible near latest messages
          this.$nextTick && this.$nextTick(() => {
            if (this.scrollTranscriptToBottom) this.scrollTranscriptToBottom();
          });
        } else {
          this.addErrorLog && this.addErrorLog(data.error || 'Failed to load conversation', 'selectConversation');
        }
      } catch (e) {
        this.addErrorLog && this.addErrorLog(`Network error: ${e.message}`, 'selectConversation');
      }
    },

    async deleteConversation(conv) {
      if (!confirm('Delete this conversation?')) return;
      try {
        const { response, data } = await this.fetchJsonOrError(`${this.apiBase}/conversations/${encodeURIComponent(conv.id)}/delete/`, { method: 'DELETE' });
        if (response.ok && data.success) {
          this.conversations = this.conversations.filter(c => c.id !== conv.id);
          if (this.activeConversation && this.activeConversation.id === conv.id) this.activeConversation = null;
        } else {
          this.addErrorLog && this.addErrorLog(data.error || 'Failed to delete conversation', 'deleteConversation');
        }
      } catch (e) {
        this.addErrorLog && this.addErrorLog(`Network error: ${e.message}`, 'deleteConversation');
      }
    }
  }
};

