# Ceto Chat Frontend (ESM) – How the JS modules interact

This document explains the rationale and architecture of the Ceto Chat frontend that lives under `gaia/djangaia/ceto_chat/static/ceto_chat/`. It focuses on:
- Why we use native ES Modules (ESM)
- Which files exist and how they depend on each other
- How Dependency Inversion is respected in the design
- How to safely add new components and modules without breaking dependency loading


## Why ESM (and why now)

We intentionally use the browser’s native ES Module system instead of a bundler:
- Simpler: No build step or tooling required. Django serves the static files as-is.
- Explicit graph: Dependencies are declared via `import` / `export` so the module graph is easy to reason about.
- Better isolation: Each file has its own scope; no accidental globals.
- Progressive migration: We can migrate incrementally from legacy globals to ESM.

Important: ESM files must be loaded either via a `<script type="module" ...>` entrypoint or imported from other ESM modules. Do not include ESM files using classic `<script src>` tags (that will cause “Unexpected token export”).


## How files are loaded

The base Django template `templates/ceto_chat/ceto_chat_base.html`:
- Defines an `importmap` for Vue:
  ```html
  <script type="importmap">{"imports": {"vue": "https://unpkg.com/vue@3.4.38/dist/vue.esm-browser.js"}}</script>
  ```
- Loads the ESM entrypoint only:
  ```html
  <script type="module" src="/static/ceto_chat/ceto_app.module.js"></script>
  ```
- Exposes `window.MCP_API_BASE` for the frontend to target the MCP HTTP server.

From there:
- `ceto_app.module.js` is the root that imports and registers UI components, mixes in shared logic, and performs dynamic imports for providers/utilities when needed.
- All other files are pulled in via ESM imports from the entrypoint or from files it imports.


## Module map and responsibilities

Top-level entry
- `ceto_app.module.js` (ESM)
  - Creates the Vue app, registers components, mixes in Debug and Conversations.
  - Dynamically imports `chat_providers.js` to choose Live vs Mock provider.
  - Uses provider methods for tool listing, tool calling, and sending chat.
  - Provides a small helper `scrollTranscriptToBottom()`.

UI components
- `chat_response.module.js` (ESM)
  - Unified response renderer (both chat and tool). Register/use as `chat-response-panel` with `:response`.
- `conversations_panel.module.js` (ESM)
  - Sidebar component for listing/selecting/deleting conversations (click row to open; delete button stops click propagation).
- `debug.js` (ESM)
  - Exports `DebugPanelMixin` and `DebugPanelComponent` used to render the debug side panel.

Cross-cutting mixins and utilities
- `conversations.js` (ESM)
  - Exports `ConversationsMixin` with state and methods to load/create/select/delete conversations against `/ceto_chat/api/conversations/*`.
  - Keeps the URL in sync using `?conversation_id=<id>` and auto-loads that conversation on startup.
  - When a conversation is loaded, sets `selectedToolName = 'raw_chat'` so the transcript is visible.
- `chat_mcp_utils.js` (ESM)
  - Utility helpers to talk to the MCP HTTP endpoints (load tools, call tool, send chat).
  - Uses `window.MCP_API_BASE` and falls back to mock behavior if the MCP server fails.

Providers and mocks (Dependency Inversion)
- `chat_providers.js` (ESM)
  - Defines two providers with the same interface:
    - `LiveProvider`: delegates to `chat_mcp_utils.js` for real MCP calls
    - `MockProvider`: uses local fixtures in `mock_tools.js` and `mock_chat.js`
  - Exports `select(ctx)` which picks a provider at runtime based on connectivity; `ceto_app.module.js` depends on this abstraction, not on a concrete implementation.
- `mock_tools.js` (ESM)
  - Exports `CetoMockTools` object. Each tool exposes an `invoke(ctx, toolArgs)` that returns a mocked JSON-RPC-like result.
- `mock_chat.js` (ESM)
  - Exports `mockChat(message, ctx)` which returns a canned chat response. Used when MCP is unavailable.

Styling
- `ceto_chat.css`
  - Contains layout for the two-column UI, the scrollable transcript area, and the sticky input bar.


## Dependency Inversion in practice

- The app (UI layer) depends on the `Provider` interface, not on the concrete MCP or mock implementations.
  - `ceto_app.module.js` imports `select()` from `chat_providers.js` and then calls the provider’s `loadTools`, `callTool`, and `sendChat` methods.
  - This means the UI is unaware of how tools or chat are fulfilled (live HTTP vs mock fixtures).
- Lower-level utilities (`chat_mcp_utils.js`) never import UI components. They are pure logic modules receiving context from the caller (`ctx`) for logging and error handling.
- Mock implementations are plug-compatible and live under the provider layer.
- The conversations system (`conversations.js`) exposes a mixin the UI can adopt, but it communicates strictly via REST endpoints; it doesn’t reach into the provider internals.

This design enables swapping providers, adding new backends, or testing with mocks without changing the UI code that renders lists, inputs, and transcripts.


## Runtime flow (high level)

1. Template loads `ceto_app.module.js` with `<script type="module">`.
2. The app mounts and calls `loadMcpTools()`.
3. `loadMcpTools()` dynamically imports `chat_providers.js`, selects `LiveProvider` or `MockProvider`, then calls `provider.loadTools(this)`.
4. The sidebar component renders conversations using `ConversationsMixin`; clicking a conversation loads it, updates the URL, and populates `messages`.
5. The center pane shows transcript for `raw_chat` and sticky input for sending new messages.
6. On send, the provider’s `sendChat()` is used. Messages are appended and auto-saved to the current conversation via `/api/conversations/<id>/append/`. If no conversation exists, one is created and the URL is updated.


## Module graph (Mermaid)

```mermaid
flowchart TD
  A[ceto_chat_base.html] -- type="module" --> B[ceto_app.module.js]
  B --> C[debug.js\n(DebugPanelMixin, DebugPanelComponent)]
  B --> D[conversations.js\n(ConversationsMixin)]
  B --> E[chat_response.module.js]
  B --> F[tool_response.module.js]
  B --> G[conversations_panel.module.js]

  B -. dynamic import .-> H[chat_providers.js]
  H --> I[LiveProvider]
  H --> J[MockProvider]
  I --> K[chat_mcp_utils.js]
  J --> L[mock_tools.js\n(CetoMockTools)]
  J --> M[mock_chat.js]

  subgraph UI Components
    E
    F
    G
  end

  subgraph Providers & Utils
    H
    I
    J
    K
    L
    M
  end

  K -.->|HTTP| MCP[MCP HTTP Server]
```

This diagram shows:
- The template loads only the ESM entrypoint.
- The entrypoint imports core UI/mixins and dynamically chooses a provider.
- Live provider calls MCP via utilities; Mock provider uses local mocks.
- UI depends on provider abstraction, not on concrete implementations.


## Adding new components safely

Follow these guidelines to avoid breaking dependency loading:

1) Use ESM and default exports for components
- Create a file `my_widget.module.js`:
  ```js
  export default {
    name: 'MyWidget',
    props: { /* ... */ },
    template: `<div class="my-widget">Hello from MyWidget</div>`
  };
  ```
- Register it in the app (eager import):
  ```js
  import MyWidget from './my_widget.module.js';
  app.component('my-widget', MyWidget);
  ```
- Or register it lazily where used:
  ```js
  const MyWidget = (await import('./my_widget.module.js')).default;
  app.component('my-widget', MyWidget);
  ```

2) Keep directions of dependency clean
- UI components can import mixins/utilities, not vice‑versa.
- Providers can import utilities and mocks, not UI.
- Utilities should be side‑effect free and avoid touching the DOM.

3) Avoid globals and classic `<script>` tags for modules
- Do not add `<script src="...my_es_module.js">` without `type="module"`.
- Let `ceto_app.module.js` import new modules instead of adding extra tags to HTML.

4) Respect provider abstraction
- If you need new chat behavior (e.g., streaming, another backend), add a new provider with the same shape:
  ```js
  export const MyProvider = {
    name: 'MyProvider',
    async loadTools(ctx) { /* ... */ },
    async callTool(ctx, toolName, toolArgs) { /* ... */ },
    async sendChat(ctx, messageText) { /* ... */ }
  };
  ```
- Update `chat_providers.js` to export it and extend `select(ctx)` logic if needed.

5) Add utilities as pure functions
- Put reusable logic into `*.js` files that export functions/classes.
- Accept all external services via arguments (URLs, tokens, ctx) instead of importing app state.

6) Prevent circular imports
- Components should not import each other cyclically. If two parts need to talk, factor a shared utility or an event/method on the parent app.
- If you sense a potential cycle, prefer dynamic `import()` at the leaf call site.

7) Keep templates small and focused
- If a component grows large, extract subsections into their own `*.module.js` components and register them under a namespace (e.g., `mcp-...`).

8) Verify in the browser
- Hard refresh to bust caches after changing import graphs.
- Watch the console for ESM and Vue warnings (unresolved specifiers, v-if/v-else adjacency, etc.).
- Use Network panel to ensure modules load with status 200.


## Quick reference: key files

- Entry: `ceto_app.module.js`
- Providers: `chat_providers.js` (selects `LiveProvider` vs `MockProvider`)
- MCP utilities: `chat_mcp_utils.js`
- Mocks: `mock_tools.js`, `mock_chat.js`
- Conversations: `conversations.js` (mixin), `conversations_panel.module.js` (component)
- Debug UI: `debug.js` (mixin + component)
- Styles: `ceto_chat.css`

With this structure, you can add new UI components, backends, or utilities without changing how the app boots or how dependencies are wired.

