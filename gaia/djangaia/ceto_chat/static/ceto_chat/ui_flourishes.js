// ui_flourishes.js
// Optional, zero-coupling UI effects for <PERSON><PERSON> (ESM)
// - Listens to ceto:chat:* DOM events emitted by the app
// - Adds a lightweight spinner and visual affordances during send/receive
// - Pure progressive enhancement: if not included, nothing changes; if included, no app logic is modified
// - Safe to include multiple times: idempotent registration

let registered = false;
let handlers = [];
let spinnerEl = null;

const STYLE_ID = 'ceto-ui-flourishes-style';
const SPINNER_ID = 'ceto-ui-spinner';

function ensureStyle() {
  if (typeof document === 'undefined') return;
  if (document.getElementById(STYLE_ID)) return;
  const css = `
    @keyframes ceto-spin { to { transform: rotate(360deg); } }
    .ceto-spinner { position: fixed; right: 12px; bottom: 12px; width: 18px; height: 18px;
      border: 2px solid #bbb; border-top-color: #333; border-radius: 50%;
      animation: ceto-spin .9s linear infinite; opacity: 0; pointer-events: none; transition: opacity .15s; z-index: 2000; }
    .ceto-spinner.show { opacity: 1; }
    @media (prefers-reduced-motion: reduce) { .ceto-spinner { animation: none; } }
    /* Optional: subtle pulse for send button when busy (no functional change) */
    @keyframes ceto-pulse { 0%,100%{ transform: scale(1); } 50%{ transform: scale(1.03); } }
    .ceto-send-busy { animation: ceto-pulse 900ms ease-in-out infinite; }
    /* Input/btn visual state only (no disabling) */
    .ceto-aria-busy { outline: 2px solid rgba(102,204,255,0.35); outline-offset: 2px; }
  `;
  const style = document.createElement('style');
  style.id = STYLE_ID;
  style.textContent = css;
  document.head.appendChild(style);
}

function ensureSpinner() {
  if (typeof document === 'undefined') return null;
  let el = document.getElementById(SPINNER_ID);
  if (!el) {
    el = document.createElement('div');
    el.id = SPINNER_ID;
    el.className = 'ceto-spinner';
    el.setAttribute('aria-hidden', 'true');
    document.body.appendChild(el);
  }
  return el;
}

const showSpinner = () => { if (spinnerEl) spinnerEl.classList.add('show'); };
const hideSpinner = () => { if (spinnerEl) spinnerEl.classList.remove('show'); };

function setBusyVisual(on) {
  if (typeof document === 'undefined') return;
  const input = document.querySelector('.chat-input-row input[type="text"], .chat-input-row input');
  const btn = document.querySelector('.chat-input-row button');
  if (input) {
    if (on) input.classList.add('ceto-aria-busy'); else input.classList.remove('ceto-aria-busy');
    input.setAttribute('aria-busy', on ? 'true' : 'false');
  }
  if (btn) {
    if (on) btn.classList.add('ceto-send-busy'); else btn.classList.remove('ceto-send-busy');
    btn.setAttribute('aria-busy', on ? 'true' : 'false');
    if (on) btn.dataset.sending = 'true'; else delete btn.dataset.sending;
    btn.title = on ? 'Sending…' : btn.title?.replace(/^Sending…$/,'') || btn.title || '';
  }
}

function onEvt(type, fn) {
  const evtName = `ceto:chat:${type}`;
  const h = (e) => { try { fn((e && e.detail) || {}); } catch (_) {} };
  window.addEventListener(evtName, h);
  handlers.push([evtName, h]);
}

function addChatListeners() {
  onEvt('send-start', ({ text }) => { setBusyVisual(true); showSpinner(); });
  onEvt('receive-pending', () => { showSpinner(); });
  onEvt('receive-message', ({ text /*, delta*/ }) => { showSpinner(); });
  onEvt('receive-end', ({ text }) => { hideSpinner(); setBusyVisual(false); });
  onEvt('error', ({ message }) => { hideSpinner(); setBusyVisual(false); });
}

export function register() {
  if (registered) return;
  if (typeof window === 'undefined' || typeof document === 'undefined') return;
  ensureStyle();
  spinnerEl = ensureSpinner();
  addChatListeners();
  registered = true;
}

export function unregister() {
  if (!registered) return;
  handlers.forEach(([evtName, h]) => window.removeEventListener(evtName, h));
  handlers.length = 0;
  if (spinnerEl && spinnerEl.parentNode) spinnerEl.parentNode.removeChild(spinnerEl);
  spinnerEl = null;
  registered = false;
}

try { window.CetoUiFlourishes = { register, unregister }; } catch (_) {}

// Auto-register on import for convenience
register();

export default { register, unregister };

