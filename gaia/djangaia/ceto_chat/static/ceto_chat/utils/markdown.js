// Markdown utilities for Ceto Chat
// Very small Markdown-to-HTML converter (safe subset)
export function markdownToHtml(md) {
  if (!md) return '';
  let html = md;
  // Escape HTML
  html = html.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
  // Headings
  html = html.replace(/^###### (.*)$/gm, '<h6>$1</h6>')
             .replace(/^##### (.*)$/gm, '<h5>$1</h5>')
             .replace(/^#### (.*)$/gm, '<h4>$1</h4>')
             .replace(/^### (.*)$/gm, '<h3>$1</h3>')
             .replace(/^## (.*)$/gm, '<h2>$1</h2>')
             .replace(/^# (.*)$/gm, '<h1>$1</h1>');
  // Code blocks ```
  html = html.replace(/```([\s\S]*?)```/gm, (m, code) => `<pre><code>${code}</code></pre>`);
  // Inline code
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
  // Bold/italic
  html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
             .replace(/\*([^*]+)\*/g, '<em>$1</em>');
  // Links [text](url)
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener">$1</a>');
  // Lists
  html = html.replace(/(^|\n)[*-] (.*)(?=\n|$)/g, '$1<ul><li>$2</li></ul>');
  // Paragraphs
  html = html.replace(/(^|\n)([^<\n][^\n]*)/g, (m, p1, text) => `${p1}<p>${text}</p>`);
  // Collapse multiple <ul>
  html = html.replace(/<\/ul>\n<ul>/g, '');
  return html;
}

// Preferred renderer using marked (CDN ESM). Falls back to markdownToHtml
export async function renderMarkdown(md) {
  try {
    const { marked } = await import('https://cdn.jsdelivr.net/npm/marked/lib/marked.esm.js');
    return marked.parse(md);
  } catch (e) {
    console.warn('Falling back to simple markdown renderer:', e?.message || e);
    return markdownToHtml(md);
  }
}

export default { markdownToHtml, renderMarkdown };

