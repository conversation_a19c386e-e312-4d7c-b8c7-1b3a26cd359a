from django.urls import path
from django.contrib.auth import views as auth_views
from . import views
from . import api

app_name = 'ceto_chat'

urlpatterns = [
    path('', views.ceto_chat_app, name='ceto_chat_app'),
    # Auth
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('register/', views.register_view, name='register'),
    path('profile/', views.profile_view, name='profile'),
    path('profile/change-password/', views.change_password_view, name='change_password'),
    # Password reset
    path('password-reset/',
         auth_views.PasswordResetView.as_view(
             template_name='ceto_chat/auth/password_reset.html',
             email_template_name='ceto_chat/auth/password_reset_email.html',
             subject_template_name='ceto_chat/auth/password_reset_subject.txt',
             success_url='/ceto_chat/password-reset/done/'
         ),
         name='password_reset'),
    path('password-reset/done/',
         auth_views.PasswordResetDoneView.as_view(
             template_name='ceto_chat/auth/password_reset_done.html'
         ),
         name='password_reset_done'),
    path('password-reset-confirm/<uidb64>/<token>/',
         auth_views.PasswordResetConfirmView.as_view(
             template_name='ceto_chat/auth/password_reset_confirm.html',
             success_url='/ceto_chat/password-reset-complete/'
         ),
         name='password_reset_confirm'),
    path('password-reset-complete/',
         auth_views.PasswordResetCompleteView.as_view(
             template_name='ceto_chat/auth/password_reset_complete.html'
         ),
         name='password_reset_complete'),

    # Conversation API endpoints (Django-based, JSON store)
    path('api/conversations/', api.list_conversations, name='api_list_conversations'),
    path('api/conversations/create/', api.create_conversation, name='api_create_conversation'),
    path('api/conversations/<str:conversation_id>/', api.load_conversation, name='api_load_conversation'),
    path('api/conversations/<str:conversation_id>/delete/', api.delete_conversation, name='api_delete_conversation'),
    path('api/conversations/<str:conversation_id>/update/', api.update_conversation, name='api_update_conversation'),
    path('api/conversations/<str:conversation_id>/append/', api.append_messages, name='api_append_messages'),
]
